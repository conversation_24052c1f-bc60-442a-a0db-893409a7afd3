# Arcaai EMR Microservice API Documentation

## Table of Contents
1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Base URL](#base-url)
4. [Common Response Format](#common-response-format)
5. [Error Handling](#error-handling)
6. [API Endpoints](#api-endpoints)
   - [Authentication APIs](#authentication-apis)
   - [User Management APIs](#user-management-apis)
   - [Organization APIs](#organization-apis)
   - [Patient APIs](#patient-apis)
   - [Doctor APIs](#doctor-apis)
   - [Appointment APIs](#appointment-apis)
   - [Medicine APIs](#medicine-apis)
   - [Lab Test APIs](#lab-test-apis)
   - [Prescription APIs](#prescription-apis)
   - [Dashboard APIs](#dashboard-apis)
   - [Permission & Role APIs](#permission--role-apis)

## Overview

The Arcaai EMR (Electronic Medical Records) Microservice provides a comprehensive API for managing healthcare data including patients, doctors, appointments, prescriptions, lab tests, and organizational information. The system supports role-based access control with different permission levels for various user types.

## Authentication

The API uses JWT (JSON Web Token) based authentication with role-based access control.

### Authentication Header
```
Authorization: Bearer <jwt_token>
```

### User Roles
- **Super Admin**: Full system access
- **Organization Admin**: Organization-level management
- **Doctor**: Medical professional access
- **Nurse**: Nursing staff access  
- **Receptionist**: Front desk operations

## Base URL
```
https://emr-ms-dev-apim.azure-api.net/EMR-MS/api/v0.1
```

## Common Response Format

### Success Response
```json
{
  "data": {},
  "message": "Success message",
  "status": 200
}
```

### Error Response

```json
{
  "error": "Error message",
  "status": 400|401|403|404|500
}
```

## Error Handling

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input parameters |
| 401 | Unauthorized - Missing or invalid authentication |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 500 | Internal Server Error - Server-side error |

## API Endpoints

### Authentication APIs

#### Login
**POST** `/auth/login`

Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name",
    "userRole": "doctor",
    "organizationId": "org-id",
    "organizationName": "Hospital Name"
  },
  "token": "jwt-token",
  "redirectUrl": "https://app.com/emr",
  "permissionKeys": ["emr.access", "emr.patientinfo.view"]
}
```

#### Reset Password
**POST** `/auth/reset-password`

Reset user password using reset token.

**Request Body:**
```json
{
  "token": "reset-token",
  "newPassword": "newPassword123"
}
```

### User Management APIs

#### Get User Details
**GET** `/user?email={email}`
**GET** `/user?userType={userType}`

Retrieve user information by email or user type.

**Query Parameters:**
- `email` (string): User email address
- `userType` (string): Filter by user type (doctor, nurse, etc.)

**Response:**
```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "name": "User Name",
  "userRole": "doctor",
  "organizationId": "org-id",
  "organizationName": "Hospital Name",
  "isActive": true
}
```

#### Create User
**POST** `/user`

Create a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "name": "New User",
  "userRole": "doctor",
  "organizationId": "org-id",
  "phone": "+**********"
}
```

#### Update User
**PATCH** `/user?userId={userId}`

Update user information (email and organizationId cannot be changed).

**Request Body:**
```json
{
  "name": "Updated Name",
  "phone": "+**********",
  "isActive": true
}
```

#### Delete User
**DELETE** `/user?userId={userId}`

Delete a user from both the local database and Azure B2C.

**Query Parameters:**
- `userId` (string, required): The ID of the user to delete

**Response:**
```json
{
  "message": "User deleted successfully",
  "userId": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing userId parameter
- `403 Forbidden`: Super Admin users cannot be deleted
- `404 Not Found`: User not found
- `500 Internal Server Error`: Failed to delete user

**Notes:**
- Super Admin users cannot be deleted for security reasons
- The user will be deleted from both the local Cosmos DB container and Azure B2C
- If the user doesn't exist in Azure B2C but exists locally, only the local deletion will be performed
- If the user has no B2C ID, only local deletion will be performed
- This operation cannot be undone

#### Get Users List
**GET** `/user/list`

Get paginated list of users with filtering options.

**Query Parameters:**
- `organizationId` (string, optional): Filter by organization
- `search` (string, optional): Search by name or email
- `role` (string, optional): Filter by user role
- `isActive` (string, optional): Filter by active status
- `sortBy` (string, optional): Sort field (default: name)
- `sortOrder` (string, optional): Sort order (asc/desc)
- `pageSize` (number, optional): Items per page (default: 10)
- `page` (number, optional): Page number (default: 1)
- `continueToken` (string, optional): Pagination token

#### Set Password
**POST** `/user/set-password`

Set password for user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "newPassword123"
}
```

### Organization APIs

#### List Organizations
**GET** `/list-organizations`

Get list of all organizations.

**Query Parameters:**
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token

#### Get Organization
**GET** `/organization?id={organizationId}`

Get organization details by ID.

#### Create Organization
**POST** `/organization`

Create a new organization.

**Request Body:**
```json
{
  "name": "Hospital Name",
  "contactEmail": "<EMAIL>",
  "contactPersonName": "Contact Person",
  "contactPhone": "+**********",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345",
    "country": "Country"
  },
  "description": "Hospital description"
}
```

#### Update Organization
**PATCH** `/organization?id={organizationId}`

Update organization information.

#### Delete Organization
**DELETE** `/organization?id={organizationId}`

Delete an organization.

### Patient APIs

#### Get Patient
**GET** `/patient?id={patientId}`

Get patient profile by ID.

**Query Parameters:**
- `id` (string, required): Patient ID
- `last_consultation_date` (string, optional): Filter by consultation date

#### Create Patient
**POST** `/patient`

Create a new patient record.

**Request Body:**
```json
{
  "name": "Patient Name",
  "sex": "Male",
  "dob": "1990-01-01",
  "height": "175",
  "weight": "70",
  "address": "Patient Address",
  "aadhar": "1234-5678-9012",
  "abha": "ABHA-ID",
  "contact": {
    "phone": "+**********",
    "email": "<EMAIL>"
  },
  "insurance": {
    "provider": "Insurance Company",
    "id": "INS-123",
    "proof": "proof-document-url"
  }
}
```

#### Update Patient
**PUT** `/patient`
**PATCH** `/patient`

Update patient information.

#### Search Patients
**POST** `/patient/search`

Search patients with pagination.

**Request Body:**
```json
{
  "query": "search term",
  "pagesize": 10,
  "continuetoken": "pagination-token"
}
```

#### Get Organization Patients
**GET** `/organization/patients`

Get patients for a specific organization with pagination.

**Query Parameters:**
- `organizationId` (string): Organization ID
- `search` (string, optional): Search term
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token

### Doctor APIs

#### Get Doctor
**GET** `/doctor?id={doctorId}`
**GET** `/doctor?email={email}`

Get doctor profile by ID or email.

#### Create Doctor
**POST** `/doctor`

Create a new doctor profile.

**Request Body:**
```json
{
  "username": "doctor123",
  "general": {
    "fullName": "Dr. John Doe",
    "designation": "Cardiologist",
    "department": "Cardiology",
    "doctorID": "DOC-001",
    "contactNumber": "+**********",
    "workEmail": "<EMAIL>"
  },
  "personalDetails": {
    "dateOfBirth": "1980-01-01",
    "gender": "Male",
    "nationality": "Indian",
    "religion": "Hindu",
    "maritalStatus": "Married"
  },
  "professionalDetails": {
    "specialties": ["Cardiology"],
    "qualifications": [
      {
        "degree": "MBBS",
        "specialization": "General Medicine",
        "university": "Medical University",
        "yearOfCompletion": "2005"
      }
    ]
  }
}
```

#### Update Doctor

**PUT** `/doctor`
**PATCH** `/doctor?id={doctorId}`

Update doctor profile information.

#### Get/Delete Doctor Profile Picture URL

**GET** `/doctor/profile-picture/url?doctorId={doctorId}`
**DELETE** `/doctor/profile-picture/url?doctorId={doctorId}`

Retrieves or deletes the URL for a doctor's profile picture.

**Query Parameters:**

- `doctorId` (string, required): The ID of the doctor.

**GET Success Response:**

```json

{
  "profilePictureUrl": "https://[storage_account_name].blob.core.windows.net/[container_name]/[blob_name]"
}
```

### Appointment APIs

#### Get Appointments

**GET** `/appointment?doctorId={doctorId}`
**GET** `/appointment?doctorId={doctorId}&date={date}`

Get appointments for a doctor, optionally filtered by date.

#### Create Appointment
**POST** `/appointment?doctorId={doctorId}`

Create a new appointment.

**Request Body:**
```json
{
  "patientId": "patient-id",
  "appointmentDate": "2024-01-01",
  "appointmentTime": "10:00",
  "duration": 30,
  "reason": "Regular checkup",
  "status": "scheduled"
}
```

#### Update Appointment
**PUT** `/appointment?doctorId={doctorId}`
**PATCH** `/appointment?appointmentId={appointmentId}`

Update appointment details.

### Medicine APIs

#### Get All Medicines
**GET** `/medicine`

Get list of all medicines.

#### Seed Medicines
**POST** `/medicine`

Seed medicines from Excel file.

#### Search Medicines
**POST** `/medicine/search`
**POST** `/medicine/search?organizationId={organizationId}`

Search medicines with pagination. Supports both general medicine search and organization-specific search.

**Query Parameters:**
- `organizationId` (string, optional): When provided, searches in OrganizationMedicines container for organization-specific medicines

**Request Body:**
```json
{
  "searchText": "medicine name",
  "pageSize": 10,
  "continuationToken": "token"
}
```

**Behavior:**
- Without `organizationId`: Searches in the general medicines container
- With `organizationId`:
  1. Gets active medicine IDs from OrganizationMedicines container for the organization
  2. Searches in the medicines container using those medicine IDs and the search text
  3. Returns only medicines that are both assigned to the organization and match the search criteria

#### Get Organization Medicines
**GET** `/organization/medicines`

Get medicines available for an organization.

**Query Parameters:**
- `organizationId` (string): Organization ID
- `search` (string, optional): Search term
- `isActive` (boolean, optional): Filter by active status
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token

### Lab Test APIs

#### Get Lab Tests
**GET** `/lab-tests`

Get list of all lab tests.

#### Add Lab Test
**POST** `/lab-tests`

Add a new lab test.

#### Search Lab Tests
**POST** `/lab-tests/search`
**POST** `/lab-tests/search?organizationId={organizationId}`

Search lab tests with filters. Supports both general lab test search and organization-specific search.

**Query Parameters:**
- `organizationId` (string, optional): When provided, searches in OrganizationTests container for organization-specific lab tests

**Request Body:**
```json
{
  "searchText": "blood test",
  "pageSize": 1000,
  "continuationToken": "",
  "department": "CHEMISTRY"
}
```

**Behavior:**
- Without `organizationId`: Searches in the general lab_tests container
- With `organizationId`:
  1. Gets active test IDs from OrganizationTests container for the organization
  2. Searches in the lab_tests container using those test IDs and the search criteria
  3. Returns only lab tests that are both assigned to the organization and match the search criteria
  4. Includes organization-specific pricing and department information

#### Get Departments
**GET** `/lab-test/departments`

Get list of lab test departments.

#### Get LOINC List
**GET** `/loinc/list`

Get paginated LOINC test list.

**Query Parameters:**
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token
- `isActive` (boolean, optional): Filter by active status

#### Update LOINC Tests
**POST** `/loinc/update`

Update organization test details (bulk operation).

**Request Body:**
```json
{
  "organizationId": "org-123",
  "selectAll": true,
  "department": "Clinical Chemistry",
  "async": true
}
```

**Response (Async):**
```json
{
  "message": "Organization test update started",
  "jobId": "job-id",
  "async": true,
  "statusUrl": "/api/loinc/update/status/job-id"
}
```

#### Get Update Status
**GET** `/loinc/update/status/{jobId}`

Get status of bulk update operation.

#### Get LOINC Tests for Organization
**GET** `/loinc/tests-for-organization`

Get LOINC tests configured for an organization.

### Prescription APIs

#### Get Prescriptions
**GET** `/prescriptions`

Get patient prescriptions.

**Query Parameters:**
- `patientId` (string): Patient ID
- `doctorId` (string, optional): Filter by doctor
- `pageSize` (number, optional): Items per page
- `continueToken` (string, optional): Pagination token

#### Get Prescription Details
**GET** `/prescriptions/details`

Get detailed prescription information.

#### Search Prescriptions
**POST** `/prescriptions/search`

Search prescriptions with filters.

### Dashboard APIs

#### Get Dashboard Summary
**GET** `/dashboard/summary`

Get dashboard summary statistics.

**Query Parameters:**
- `organizationId` (string, optional): Filter by organization
- `dateRange` (string, optional): Date range filter

### Permission & Role APIs

#### Get Permissions by Role
**GET** `/permissions/api-list`

Get API permissions for a role.

**Query Parameters:**
- `roleId` (string): Role ID

#### Assign Permissions
**POST** `/assign-permissions`

Assign permissions to a role.

**Request Body:**
```json
{
  "roleId": "role-id",
  "permissions": ["permission1", "permission2"]
}
```

#### List Roles
**GET** `/list-roles`

Get list of all roles.

#### Create Role
**POST** `/role`

Create a new role.

#### Update Role
**PATCH** `/role`

Update role information.

#### Delete Role
**DELETE** `/role`

Delete a role.

## Rate Limiting

The API implements rate limiting to prevent abuse. Current limits:
- 1000 requests per hour per user
- Bulk operations have separate limits

## Pagination

Most list endpoints support pagination using continuation tokens:

**Request:**
```
GET /api/endpoint?pageSize=10&continueToken=token
```

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "pageSize": 10,
    "continueToken": "next-token",
    "hasMore": true,
    "totalCount": 100
  }
}
```

## Webhooks

The system supports webhooks for real-time notifications:
- Patient updates
- Appointment changes
- Lab result availability

## SDK and Libraries

Official SDKs available for:
- JavaScript/Node.js
- Python
- C#/.NET

### Patient Consultation APIs

#### Get Patient Consultations
**GET** `/patient-consultation?patientId={patientId}`

Get consultation history for a patient.

#### Create Patient Consultation
**POST** `/patient-consultation?patientId={patientId}`

Create a new consultation record.

**Request Body:**
```json
{
  "doctorId": "doctor-id",
  "consultationDate": "2024-01-01",
  "chiefComplaint": "Patient complaint",
  "diagnosis": "Diagnosis details",
  "treatment": "Treatment plan",
  "notes": "Additional notes"
}
```

#### Update Patient Consultation
**PUT** `/patient-consultation?patientId={patientId}`

Update consultation record.

### Patient Vitals APIs

#### Get Patient Vitals
**GET** `/patient/vitals?patientId={patientId}`

Get vital signs for a patient.

#### Create/Update Patient Vitals
**POST** `/patient/vitals?patientId={patientId}`
**PUT** `/patient/vitals?patientId={patientId}`

Record patient vital signs.

**Request Body:**
```json
{
  "height": "175",
  "weight": "70",
  "bloodPressure": {
    "systolic": 120,
    "diastolic": 80
  },
  "pulse": 72,
  "temperature": 98.6,
  "respiratoryRate": 16,
  "oxygenSaturation": 98,
  "bmi": 22.9,
  "recordedDate": "2024-01-01T10:00:00Z"
}
```

### Patient History APIs

#### Get Patient History
**GET** `/patient/history?patientId={patientId}`

Get patient medical history.

#### Create/Update Patient History
**POST** `/patient/history?patientId={patientId}`
**PUT** `/patient/history?patientId={patientId}`

Record patient medical history.

### Patient Lifestyle APIs

#### Get Patient Lifestyle
**GET** `/patient/lifestyle?patientId={patientId}`

Get patient lifestyle information.

#### Create/Update Patient Lifestyle
**POST** `/patient/lifestyle?patientId={patientId}`
**PATCH** `/patient/lifestyle?patientId={patientId}`

Record patient lifestyle data.

#### Get Lifestyle Questions
**GET** `/lifestyle/question`

Get available lifestyle assessment questions.

#### Create Lifestyle Question
**POST** `/lifestyle/question`

Create new lifestyle assessment question.

#### Lifestyle Ambient Listening
**POST** `/lifestyle/ambient-listening`

Process conversation transcript for lifestyle assessment using dynamic questions from database.

**Request Body:**
```json
{
  "transcript": "Doctor: How often do you exercise? Patient: I try to go to the gym 3 times a week...",
  "source": "lifestyle-assessment"
}
```

**Response:**
```json
{
  "conversation": [
    {
      "speaker": "doctor",
      "message": "How many major meals do you eat in a day?"
    },
    {
      "speaker": "patient",
      "message": "Usually three meals."
    }
  ],
  "summary": {
    "questions": [
      {
        "id": "food_intake",
        "title": "Food intake patterns",
        "icon": "material-symbols:dining-outline",
        "fields": [
          {
            "id": "major_meals_per_day",
            "label": "Number of major meals per day",
            "type": "radio",
            "options": ["1", "2", "3", "4", "Other"],
            "allowOtherSpecify": true,
            "modal": {
              "title": "Meals per day",
              "inputType": "number"
            },
            "value": "3"
          },
          {
            "id": "taste_rating",
            "label": "How do you rate the taste of your meals?",
            "type": "slider",
            "min": 0,
            "max": 10,
            "step": 1,
            "description": "(0=Unpleasant to 10=Delicious)",
            "value": 7
          }
        ]
      }
    ]
  }
}
```

### Patient Lab Test APIs

#### Get Patient Lab Tests
**GET** `/patient-lab-test?patientId={patientId}`

Get lab tests for a patient.

#### Create Patient Lab Test
**POST** `/patient-lab-test?patientId={patientId}`

Order lab tests for a patient.

**Request Body:**
```json
{
  "tests": [
    {
      "testId": "test-id",
      "testName": "Complete Blood Count",
      "department": "Hematology",
      "urgency": "routine",
      "instructions": "Fasting required"
    }
  ],
  "orderedBy": "doctor-id",
  "orderDate": "2024-01-01"
}
```

#### Get Lab Test Details
**GET** `/patient-lab-test/details?testId={testId}`

Get detailed lab test information.

### Nurse APIs

#### Get Nurse Profile
**GET** `/nurse?id={nurseId}`

Get nurse profile information.

#### Create Nurse Profile
**POST** `/nurse`

Create a new nurse profile.

**Request Body:**
```json
{
  "personalInfo": {
    "fullName": "Nurse Name",
    "employeeId": "NUR-001",
    "department": "ICU",
    "contactNumber": "+**********",
    "email": "<EMAIL>"
  },
  "qualifications": [
    {
      "degree": "BSN",
      "institution": "Nursing College",
      "yearOfCompletion": "2020"
    }
  ],
  "experience": [
    {
      "hospital": "Previous Hospital",
      "department": "Emergency",
      "duration": "2 years"
    }
  ]
}
```

#### Update Nurse Profile
**PUT** `/nurse`

Update nurse profile information.

### Queue Management APIs

#### Get Queue
**GET** `/queue`

Get patient queue information.

**Query Parameters:**
- `departmentId` (string, optional): Filter by department
- `doctorId` (string, optional): Filter by doctor
- `date` (string, optional): Filter by date

#### Add to Queue
**POST** `/queue`

Add patient to queue.

**Request Body:**
```json
{
  "patientId": "patient-id",
  "doctorId": "doctor-id",
  "appointmentId": "appointment-id",
  "priority": "normal",
  "estimatedTime": "10:30"
}
```

#### Update Queue Status
**PATCH** `/queue?queueId={queueId}`

Update patient queue status.

#### Update Queue By ID
**PATCH** `/book-consultation/queue?queueId={queueId}`

Update queue information by queue ID.

**Query Parameters:**
- `queueId` (string, required): The ID of the queue to update.

**Request Body:**
```json
{
  "time": "value1",
  "date": "value2"
}
```

### Appointment Queue APIs

#### Get Appointment Queue
**GET** `/appointment/queue`

Get appointment queue for a specific date/doctor.

### Test Package APIs

#### Get Test Packages
**GET** `/package`

Get available test packages.

#### Create Test Package
**POST** `/package`

Create a new test package.

**Request Body:**
```json
{
  "name": "Basic Health Package",
  "type": "department",
  "tests": [
    {
      "testId": "test-1",
      "testName": "Complete Blood Count"
    },
    {
      "testId": "test-2",
      "testName": "Lipid Profile"
    }
  ],
  "price": 1500,
  "description": "Basic health screening package"
}
```

#### Update Test Package
**PATCH** `/package?packageId={packageId}`

Update test package information.

#### Get Tests for Package
**GET** `/package/tests?packageId={packageId}`

Get tests included in a specific package.

### Prescription Package APIs

#### Get Prescription Packages
**GET** `/prescription-package`

Get available prescription packages.

#### Create Prescription Package
**POST** `/prescription-package`

Create a new prescription package.

### Lab Report Document APIs

#### Upload Lab Report
**POST** `/lab-report/upload`

Upload lab report document.

**Request Body (multipart/form-data):**
```
file: [lab-report.pdf]
patientId: patient-id
testId: test-id
reportDate: 2024-01-01
```

#### Preview Lab Report
**GET** `/lab-report/preview?reportId={reportId}`

Preview uploaded lab report.

### Doctor Document Upload APIs

#### Upload Doctor Document
**POST** `/doctor/document/upload`

Upload doctor-related documents.

### Summary APIs

#### Get Summary
**GET** `/summary`

Get various summary reports.

**Query Parameters:**
- `type` (string): Summary type (patients, appointments, revenue)
- `organizationId` (string, optional): Filter by organization
- `dateRange` (string, optional): Date range filter

### Customization APIs

#### Get EMR Customization
**GET** `/customise-emr`

Get EMR customization settings.

#### Update EMR Customization
**POST** `/customise-emr`

Update EMR customization settings.

#### Get Doctor EMR Customization
**GET** `/doctor-customise-emr`

Get doctor-specific EMR customization.

#### Update Doctor EMR Customization
**POST** `/doctor-customise-emr`

Update doctor-specific EMR customization.

### Consultant APIs

#### Get Consultants
**GET** `/consultant`

Get list of consultants.

#### Create Consultant
**POST** `/consultant`

Create a new consultant profile.

### Proxy APIs

#### ICD Proxy
**GET** `/icd-proxy`

Proxy for ICD (International Classification of Diseases) API.

#### SNOMED Proxy
**GET** `/snomed-proxy`

Proxy for SNOMED CT terminology API.

### Patient Diagnosis Notes APIs

#### Get Diagnosis Notes
**GET** `/patient/diagnosis-notes?patientId={patientId}`

Get diagnosis notes for a patient.

#### Create Diagnosis Notes
**POST** `/patient/diagnosis-notes?patientId={patientId}`

Create diagnosis notes.

#### Update Diagnosis Notes
**PATCH** `/patient/diagnosis-notes?patientId={patientId}`

Update diagnosis notes.

### Patient Lifestyle Notes APIs

#### Get Lifestyle Notes
**GET** `/patient/lifestyle/note?patientId={patientId}`

Get lifestyle notes for a patient.

#### Create Lifestyle Notes
**POST** `/patient/lifestyle/note?patientId={patientId}`

Create lifestyle notes.

#### Update Lifestyle Notes
**PATCH** `/patient/lifestyle/note?patientId={patientId}`

Update lifestyle notes.

### Patient Medical History Addiction APIs

#### Get Patient Medical History Addiction
**GET** `/patient/lifestyle/medical-history-addiction?patientId={patientId}`

Get medical history addiction information for a patient including substance use history, diagnosis records, and nicotine dependence testing.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Response:**
```json
{
  "id": "medical-history-id",
  "patientId": "patient-id",
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "3 Years",
      "status": "active",
      "treatmentHistory": "Medication and lifestyle changes"
    }
  ],
  "smoking": {
    "history": "former",
    "count": "10",
    "frequency": "daily"
  },
  "alcohol": {
    "history": "current",
    "count": "2-3",
    "frequency": "2-3_times_week"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": {
    "id": "test-id",
    "responses": {
      "timeToFirstCigarette": "Within 5 minutes",
      "findDifficult": "Yes",
      "whichCigarette": "First one in the morning",
      "cigarettesPerDay": "21-30",
      "moreFrequentMorning": "Yes",
      "smokeWhenIll": "Yes"
    },
    "testDate": "2024-01-15T10:00:00Z"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-15T14:30:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter
- `404 Not Found`: No medical history found for the patient
- `500 Internal Server Error`: Server error

#### Create Patient Medical History Addiction
**POST** `/patient/lifestyle/medical-history-addiction?patientId={patientId}`

Create medical history addiction record for a patient.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Request Body:**
```json
{
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "3 Years",
      "status": "active",
      "treatmentHistory": "Medication and lifestyle changes"
    }
  ],
  "smoking": {
    "history": "former",
    "count": "10",
    "frequency": "daily"
  },
  "alcohol": {
    "history": "current",
    "count": "2-3",
    "frequency": "2-3_times_week"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": {
    "responses": {
      "timeToFirstCigarette": "Within 5 minutes",
      "findDifficult": "Yes",
      "whichCigarette": "First one in the morning",
      "cigarettesPerDay": "21-30",
      "moreFrequentMorning": "Yes",
      "smokeWhenIll": "Yes"
    },
    "testDate": "2024-01-15T10:00:00Z"
  }
}
```

**Response:**
```json
{
  "id": "medical-history-id",
  "patientId": "patient-id",
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "3 Years",
      "status": "active",
      "treatmentHistory": "Medication and lifestyle changes"
    }
  ],
  "smoking": {
    "history": "former",
    "count": "10",
    "frequency": "daily"
  },
  "alcohol": {
    "history": "current",
    "count": "2-3",
    "frequency": "2-3_times_week"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": {
    "id": "test-id",
    "responses": {
      "timeToFirstCigarette": "Within 5 minutes",
      "findDifficult": "Yes",
      "whichCigarette": "First one in the morning",
      "cigarettesPerDay": "21-30",
      "moreFrequentMorning": "Yes",
      "smokeWhenIll": "Yes"
    },
    "testDate": "2024-01-15T10:00:00Z"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-01T10:00:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter or invalid request body
- `409 Conflict`: Medical history addiction record already exists for this patient
- `500 Internal Server Error`: Server error

#### Update Patient Medical History Addiction
**PUT** `/patient/lifestyle/medical-history-addiction?id={medicalHistoryId}`

Update existing medical history addiction record.

**Query Parameters:**
- `id` (string, required): The ID of the medical history addiction record to update

**Request Body:**
```json
{
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "4 Years",
      "status": "active",
      "treatmentHistory": "Updated medication and lifestyle changes"
    },
    {
      "diseaseName": "Diabetes Type 2",
      "yearOfDiagnosis": "2023",
      "diagnosisDuration": "1 Year",
      "status": "active",
      "treatmentHistory": "Diet control and medication"
    }
  ],
  "smoking": {
    "history": "no"
  },
  "alcohol": {
    "history": "occasionally",
    "count": "1-2",
    "frequency": "4_times_month"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": null
}
```

**Response:**
```json
{
  "id": "medical-history-id",
  "patientId": "patient-id",
  "diagnosis": [
    {
      "diseaseName": "Hypertension",
      "yearOfDiagnosis": "2020",
      "diagnosisDuration": "4 Years",
      "status": "active",
      "treatmentHistory": "Updated medication and lifestyle changes"
    },
    {
      "diseaseName": "Diabetes Type 2",
      "yearOfDiagnosis": "2023",
      "diagnosisDuration": "1 Year",
      "status": "active",
      "treatmentHistory": "Diet control and medication"
    }
  ],
  "smoking": {
    "history": "no"
  },
  "alcohol": {
    "history": "occasionally",
    "count": "1-2",
    "frequency": "4_times_month"
  },
  "tobacco": {
    "history": "no"
  },
  "drugs": {
    "history": "no"
  },
  "nicotineDependenceTest": null,
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-20T16:45:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing id parameter or invalid request body
- `404 Not Found`: Medical history addiction record not found
- `500 Internal Server Error`: Server error

#### Delete Patient Medical History Addiction
**DELETE** `/patient/lifestyle/medical-history-addiction?id={medicalHistoryId}`

Delete a medical history addiction record.

**Query Parameters:**
- `id` (string, required): The ID of the medical history addiction record to delete

**Response:**
```json
{
  "message": "Medical history addiction record deleted successfully",
  "id": "medical-history-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing id parameter
- `404 Not Found`: Medical history addiction record not found
- `500 Internal Server Error`: Server error

**Notes:**
- The diagnosis array can contain multiple diagnosis records for comprehensive medical history
- Substance use history supports three states: "no", "former", and "current"
- Frequency options include: "daily", "2-3_times_week", "4_times_month", "occasionally"
- Nicotine dependence test is optional and only relevant for patients with smoking history
- All substance use fields (smoking, alcohol, tobacco, drugs) follow the same structure
- The API supports full CRUD operations for comprehensive medical history management

### Patient Demographics APIs

#### Get Patient Demographics
**GET** `/patient/lifestyle/demographics?patientId={patientId}`

Get demographic information for a patient.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Response:**
```json
{
  "id": "demographics-id",
  "patientId": "patient-id",
  "age": 35,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-75000",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345"
  },
  "emergencyContact": {
    "name": "Emergency Contact Name",
    "relationship": "Spouse",
    "phone": "+**********"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-01T10:00:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter
- `404 Not Found`: Patient demographics not found

#### Create Patient Demographics
**POST** `/patient/lifestyle/demographics?patientId={patientId}`

Create demographic information for a patient.

**Query Parameters:**
- `patientId` (string, required): The ID of the patient

**Request Body:**
```json
{
  "age": 35,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-75000",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345"
  },
  "emergencyContact": {
    "name": "Emergency Contact Name",
    "relationship": "Spouse",
    "phone": "+**********"
  }
}
```

**Response:**
```json
{
  "id": "demographics-id",
  "patientId": "patient-id",
  "age": 35,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Software Engineer",
  "education": "Bachelor's Degree",
  "income": "50000-75000",
  "address": {
    "street": "123 Main St",
    "city": "City",
    "state": "State",
    "zipCode": "12345"
  },
  "emergencyContact": {
    "name": "Emergency Contact Name",
    "relationship": "Spouse",
    "phone": "+**********"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-01T10:00:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing patientId parameter or invalid request body
- `409 Conflict`: Demographics already exists for this patient
- `400 Bad Request`: Validation errors in the request data

#### Update Patient Demographics
**PUT** `/patient/lifestyle/demographics?id={demographicsId}`

Update existing demographic information.

**Query Parameters:**
- `id` (string, required): The ID of the demographics record to update

**Request Body:**
```json
{
  "age": 36,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Senior Software Engineer",
  "education": "Master's Degree",
  "income": "75000-100000",
  "address": {
    "street": "456 Oak Ave",
    "city": "New City",
    "state": "New State",
    "zipCode": "54321"
  },
  "emergencyContact": {
    "name": "Updated Emergency Contact",
    "relationship": "Spouse",
    "phone": "+**********"
  }
}
```

**Response:**
```json
{
  "id": "demographics-id",
  "patientId": "patient-id",
  "age": 36,
  "gender": "Male",
  "ethnicity": "Asian",
  "maritalStatus": "Married",
  "occupation": "Senior Software Engineer",
  "education": "Master's Degree",
  "income": "75000-100000",
  "address": {
    "street": "456 Oak Ave",
    "city": "New City",
    "state": "New State",
    "zipCode": "54321"
  },
  "emergencyContact": {
    "name": "Updated Emergency Contact",
    "relationship": "Spouse",
    "phone": "+**********"
  },
  "created_on": "2024-01-01T10:00:00Z",
  "updated_on": "2024-01-15T14:30:00Z",
  "create_by": "user-id",
  "update_by": "user-id"
}
```

**Error Responses:**
- `400 Bad Request`: Missing id parameter or invalid request body
- `404 Not Found`: Demographics record not found
- `400 Bad Request`: Validation errors in the request data

## Data Models

### Patient Model
```json
{
  "id": "string",
  "name": "string",
  "sex": "Male|Female|Other",
  "dob": "YYYY-MM-DD",
  "height": "string",
  "weight": "string",
  "address": "string",
  "aadhar": "string",
  "abha": "string",
  "contact": {
    "phone": "string",
    "email": "string"
  },
  "insurance": {
    "provider": "string",
    "id": "string",
    "proof": "string"
  },
  "organizationId": "string",
  "createdAt": "ISO 8601 datetime",
  "updatedAt": "ISO 8601 datetime"
}
```

### Doctor Model
```json
{
  "id": "string",
  "username": "string",
  "general": {
    "fullName": "string",
    "designation": "string",
    "department": "string",
    "doctorID": "string",
    "contactNumber": "string",
    "workEmail": "string"
  },
  "personalDetails": {
    "dateOfBirth": "YYYY-MM-DD",
    "gender": "Male|Female|Other",
    "nationality": "string",
    "religion": "string",
    "maritalStatus": "string"
  },
  "professionalDetails": {
    "specialties": ["string"],
    "qualifications": [
      {
        "degree": "string",
        "specialization": "string",
        "university": "string",
        "institute": "string",
        "yearOfCompletion": "string"
      }
    ],
    "certifications": [
      {
        "name": "string",
        "regNumber": "string",
        "validFrom": "YYYY-MM-DD",
        "validTo": "YYYY-MM-DD"
      }
    ],
    "experience": [
      {
        "hospitalName": "string",
        "department": "string",
        "designation": "string",
        "from": "YYYY-MM-DD",
        "to": "YYYY-MM-DD"
      }
    ]
  }
}
```

### Organization Model
```json
{
  "id": "string",
  "name": "string",
  "contactEmail": "string",
  "contactPersonName": "string",
  "contactPhone": "string",
  "address": {
    "street": "string",
    "city": "string",
    "state": "string",
    "zipCode": "string",
    "country": "string"
  },
  "description": "string",
  "isActive": "boolean",
  "createdAt": "ISO 8601 datetime",
  "updatedAt": "ISO 8601 datetime"
}
```

### Test Model (LOINC)
```json
{
  "id": "string",
  "LOINC_NUM": "string",
  "COMPONENT": "string",
  "PROPERTY": "string",
  "TIME_ASPCT": "string",
  "SYSTEM": "string",
  "SCALE_TYP": "string",
  "METHOD_TYP": "string",
  "CLASS": "string",
  "STATUS": "string",
  "CONSUMER_NAME": "string"
}
```

### Medical History Addiction Model
```json
{
  "id": "string",
  "patientId": "string",
  "diagnosis": [
    {
      "diseaseName": "string",
      "yearOfDiagnosis": "string",
      "diagnosisDuration": "string",
      "status": "active|inactive",
      "treatmentHistory": "string"
    }
  ],
  "smoking": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "alcohol": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "tobacco": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "drugs": {
    "history": "no|former|current",
    "count": "string",
    "frequency": "daily|2-3_times_week|4_times_month|occasionally"
  },
  "nicotineDependenceTest": {
    "id": "string",
    "responses": {
      "timeToFirstCigarette": "string",
      "findDifficult": "string",
      "whichCigarette": "string",
      "cigarettesPerDay": "string",
      "moreFrequentMorning": "string",
      "smokeWhenIll": "string"
    },
    "testDate": "ISO 8601 datetime"
  },
  "created_on": "ISO 8601 datetime",
  "updated_on": "ISO 8601 datetime",
  "create_by": "string",
  "update_by": "string"
}
```

## Permission System

The API uses a comprehensive permission system with the following permission keys:

### EMR Module Permissions
- `emr.access` - Access EMR module
- `emr.patientinfo.view` - View patient information
- `emr.patientinfo.edit` - Edit patient information
- `emr.consultation.view` - View consultations
- `emr.consultation.manage` - Manage consultations
- `emr.prescription.view` - View prescriptions
- `emr.prescription.manage` - Manage prescriptions
- `emr.lab-test.view` - View lab tests
- `emr.lab-test.manage` - Manage lab tests
- `emr.doctorprofile.view` - View doctor profiles
- `emr.doctorprofile.edit` - Edit doctor profiles

### MRD Module Permissions
- `mrd.access` - Access MRD module
- `mrd.manage-patient.view` - View patient administrative data
- `mrd.manage-patient.edit` - Edit patient administrative data
- `mrd.patient-queue.manage` - Manage patient queue

### Organization Permissions
- `organization.manage` - Manage organizations
- `organization.patients.view` - View organization patients

### Role and Permission Management
- `role.manage` - Manage roles
- `permission.manage` - Manage permissions

### Dashboard Permissions
- `dashboard.view` - View dashboard

## Environment Configuration

The API behavior can be configured using environment variables:

- `TENANT_NAME` - Azure B2C tenant name
- `BASE_URL` - Application base URL
- `COSMOS_DB_ENDPOINT` - Cosmos DB endpoint
- `COSMOS_DB_KEY` - Cosmos DB access key
- `REDIS_CONNECTION_STRING` - Redis cache connection
- `BLOB_STORAGE_CONNECTION` - Azure Blob Storage connection
- `EMAIL_SERVICE_CONFIG` - Email service configuration

## Status Codes and Constants

### Lab Test Status
- `Not Paid` - Test not yet paid for
- `Awaited` - Waiting for test execution
- `Ready` - Test completed, results ready
- `Upload` - Results being uploaded
- `Uploaded` - Results uploaded and available

### Record Status
- `editable` - Record can be modified
- `finalized` - Record is finalized and cannot be modified

### Package Types
- `user` - User-specific package
- `department` - Department-specific package

## API Examples

### Complete Patient Registration Flow

1. **Create Organization** (if new)
```bash
curl -X POST "https://api.example.com/organization" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "City Hospital",
    "contactEmail": "<EMAIL>",
    "contactPersonName": "John Admin",
    "contactPhone": "+**********"
  }'
```

2. **Create Doctor User**
```bash
curl -X POST "https://api.example.com/user" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Dr. Smith",
    "userRole": "doctor",
    "organizationId": "org-123"
  }'
```

3. **Create Doctor Profile**
```bash
curl -X POST "https://api.example.com/doctor" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "drsmith",
    "general": {
      "fullName": "Dr. John Smith",
      "designation": "Cardiologist",
      "department": "Cardiology"
    }
  }'
```

4. **Register Patient**
```bash
curl -X POST "https://api.example.com/patient" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Jane Doe",
    "sex": "Female",
    "dob": "1985-05-15",
    "contact": {
      "phone": "+**********",
      "email": "<EMAIL>"
    }
  }'
```

5. **Create Appointment**
```bash
curl -X POST "https://api.example.com/appointment?doctorId=doctor-123" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "patient-456",
    "appointmentDate": "2024-01-15",
    "appointmentTime": "10:00",
    "reason": "Regular checkup"
  }'
```

6. **Record Medical History Addiction**
```bash
curl -X POST "https://api.example.com/patient/lifestyle/medical-history-addiction?patientId=patient-456" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "diagnosis": [
      {
        "diseaseName": "Hypertension",
        "yearOfDiagnosis": "2020",
        "diagnosisDuration": "3 Years",
        "status": "active",
        "treatmentHistory": "Medication and lifestyle changes"
      }
    ],
    "smoking": {
      "history": "former",
      "count": "10",
      "frequency": "daily"
    },
    "alcohol": {
      "history": "current",
      "count": "2-3",
      "frequency": "2-3_times_week"
    },
    "tobacco": {
      "history": "no"
    },
    "drugs": {
      "history": "no"
    }
  }'
```

### Lab Test Ordering Flow

1. **Search Available Tests**
```bash
curl -X POST "https://api.example.com/lab-tests/search" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "searchTerm": "blood",
    "department": "Hematology"
  }'
```

2. **Order Tests for Patient**
```bash
curl -X POST "https://api.example.com/patient-lab-test?patientId=patient-456" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "tests": [
      {
        "testId": "test-123",
        "testName": "Complete Blood Count",
        "urgency": "routine"
      }
    ],
    "orderedBy": "doctor-123"
  }'
```

3. **Upload Test Results**
```bash
curl -X POST "https://api.example.com/lab-report/upload" \
  -H "Authorization: Bearer <token>" \
  -F "file=@lab-report.pdf" \
  -F "patientId=patient-456" \
  -F "testId=test-123"
```

## Bulk Operations

### LOINC Test Updates

For large-scale LOINC test updates, the API supports both synchronous and asynchronous processing:

**Small Updates (< 500 records)**: Processed synchronously
**Large Updates (≥ 500 records)**: Processed asynchronously with job tracking

```bash
# Start bulk update
curl -X POST "https://api.example.com/loinc/update" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "organizationId": "org-123",
    "selectAll": true,
    "department": "Clinical Chemistry"
  }'

# Check status (for async operations)
curl -X GET "https://api.example.com/loinc/update/status/job-id" \
  -H "Authorization: Bearer <token>"
```

## Error Handling Examples

### Validation Errors
```json
{
  "error": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format"
    },
    {
      "field": "dob",
      "message": "Date of birth is required"
    }
  ],
  "status": 400
}
```

### Authentication Errors
```json
{
  "error": "Missing authorization token",
  "status": 401
}
```

### Permission Errors
```json
{
  "error": "User does not have permission",
  "requiredPermission": "emr.patientinfo.edit",
  "status": 403
}
```

## Testing

### Postman Collection
A comprehensive Postman collection is available for testing all API endpoints:
- Download: [EMR API Postman Collection](https://api.example.com/postman-collection)
- Environment variables included for easy setup

### Test Data
Sample test data is available for development and testing:
- Organizations, users, patients, doctors
- Lab tests, medicines, appointments
- Realistic data for comprehensive testing

## Changelog

### Version 1.2.0 (Latest)
- Added bulk LOINC update operations
- Improved pagination with continuation tokens
- Enhanced error handling and validation
- Added organization-specific filtering
- Performance optimizations for large datasets

### Version 1.1.0
- Added patient lifestyle management
- Enhanced doctor profile management
- Improved search functionality
- Added queue management features

### Version 1.0.0
- Initial API release
- Core patient, doctor, appointment management
- Basic lab test and prescription functionality
- Role-based access control

## Migration Guide

### From v1.1 to v1.2
- Update pagination parameters to use `continueToken` instead of `offset`
- Replace `pageNumber` with `page` parameter
- Update bulk operation endpoints to handle async responses

