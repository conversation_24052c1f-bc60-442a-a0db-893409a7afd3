const HttpMethod = Object.freeze({
  get: 'GET',
  post: 'POST',
  put: 'PUT',
  delete: 'DELETE',
  patch: 'PATCH',
  options: 'OPTIONS',
  head: 'HEAD',
  trace: 'TRACE',
  connect: 'CONNECT',
})

const AuthMessage = Object.freeze({
  MISSING_TOKEN: 'Missing authorization token',
  TOKEN_EXPIRED: 'Token has been expired',
  NO_PERMISSING: 'User does not have permission',
  COMMON_AUTH_FAILED: 'Login fail',
  SUCCESS: 'success',
  SESSION_EXPIRED: 'Session expired',
})
const PackageType = Object.freeze({
  USER: 'user',
  DEPARTMENT: 'department',
})
const RecordStatus = Object.freeze({
  EDITABLE: 'editable',
  FINALIZED: 'finalized',
})
const LabTestStatus = Object.freeze({
  NOT_PAID: 'Not Paid',
  AWAITED: 'Awaited',
  READY: 'Ready',
  UPLOAD: 'Upload',
  UPLOADED: 'Uploaded',
})

const AppointmentStatus = Object.freeze({
  AWAITING: 'Awaiting',
  BOOKED: 'Booked',
  PRIORITY: 'Priority',
  INVESTIGATIONS: 'Investigations',
  CONSULTATION: 'Consultation',
  DONE: 'Done',
  CANCELLED: 'Cancelled',
})

const PatientQueueStatus = Object.freeze({
  BOOKED: 'Booked',
  ARRIVED: 'Arrived',
  PROXY_VISIT: 'ProxyVisit',
  NO_SHOW: 'NoShow',
})

const PatientQueueLabStatus = Object.freeze({
  REGISTERED_NOT_PAID: 'Registered/Not paid',
  LAB_REPORT_AWAITING: 'Lab reports awaiting',
  LAB_REPORT_READY: 'Lab reports ready',
  NO_TEST_ORDERED: 'No tests ordered',
})

const PaymentStatus = Object.freeze({
  CREATED: 'created',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
})

const PaymentType = Object.freeze({
  PATIENT_REGISTRATION: 'patient_registration',
  CONSULTATION: 'consultation',
  PRESCRIPTION: 'prescription',
  LAB_TEST: 'lab_test',
})

module.exports = {
  HttpMethod,
  AuthMessage,
  PackageType,
  RecordStatus,
  LabTestStatus,
  AppointmentStatus,
  PatientQueueStatus,
  PatientQueueLabStatus,
  PaymentStatus,
  PaymentType,
}
