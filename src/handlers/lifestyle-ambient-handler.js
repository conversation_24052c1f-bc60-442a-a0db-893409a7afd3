const openAIService = require('../services/openai-service')
const lifeStyleService = require('../services/lifestyle-service')
const logging = require('../common/logging')
const helper = require('../common/helper')

class LifestyleAmbientHandler {
  async identifySpeaker(transcript) {
    try {
      var classification =
        'you are an AI assistant help doctor to separate the chat transcript to know which one is doctor which one is patient, following the format:\n\n[{"speaker": "doctor", "message": ""},{"speaker": "patient", "message": ""}]\n\ncombine into 1 inline JSON without \'\\n\'\n The result must be in the format above strictly, if you don\'t see any information to analytic then just return the json format above with the null value'
      var result = await openAIService.chatCompletion(
        classification,
        transcript,
      )
      return result
    } catch (error) {
      logging.logError('Unable to identify Speaker', error)
      return null
    }
  }

  async processLifestyleQuestions(conversation, source) {
    try {
      logging.logInfo(`Processing lifestyle questions for source: ${source}`)

      const questionsData = await lifeStyleService.getLifeStyeBySourceName(
        source,
      )

      if (!questionsData || !Array.isArray(questionsData)) {
        logging.logError(`No questions found for source: ${source}`)
        return { questions: [] }
      }

      const processedQuestions = []

      for (const questionDoc of questionsData) {
        if (questionDoc.questions && Array.isArray(questionDoc.questions)) {
          for (const question of questionDoc.questions) {
            const processedQuestion = {
              id: question.id,
              title: question.title,
              icon: question.icon,
              fields: [],
            }

            if (question.fields && Array.isArray(question.fields)) {
              for (const field of question.fields) {
                if (field.label) {
                  const processedField = { ...field }

                  try {
                    // Create a specialized prompt for extracting the value
                    const prompt = this.createValueExtractionPrompt(
                      field,
                      conversation,
                    )
                    const answer = await openAIService.chatCompletion(
                      prompt,
                      conversation,
                    )

                    processedField.value = this.processAnswerByFieldType(
                      field,
                      answer,
                    )
                  } catch (error) {
                    logging.logError(
                      `Error processing field ${field.label}:`,
                      error,
                    )
                    // Set default value based on field type
                    processedField.value =
                      this.getDefaultValueByFieldType(field)
                  }

                  processedQuestion.fields.push(processedField)
                }
              }
            }

            processedQuestions.push(processedQuestion)
          }
        }
      }

      return { questions: processedQuestions }
    } catch (error) {
      logging.logError('Unable to process lifestyle questions', error)
      return { questions: [] }
    }
  }

  createValueExtractionPrompt(field, conversation) {
    let prompt = `You are a medical AI assistant analyzing a conversation between a doctor and patient for lifestyle assessment.

From the following conversation, extract the answer for: "${field.label}"

Conversation: ${conversation}

Field Type: ${field.type}
`

    if (field.type === 'radio' && field.options) {
      prompt += `Available Options: ${field.options.join(', ')}

Instructions:
1. Find the patient's answer related to "${field.label}"
2. Match the answer to one of the available options: ${field.options.join(', ')}
3. Return ONLY the exact option that best matches the patient's response
4. If no clear match, return the closest option
5. If the patient mentions "Other" or something not in options and allowOtherSpecify is true, return "Other"
6. Do not include explanations, just return the option value

Answer:`
    } else if (field.type === 'slider') {
      prompt += `Slider Range: ${field.min} to ${field.max}
Description: ${field.description || ''}

Instructions:
1. Find the patient's numerical answer related to "${field.label}"
2. Extract the number mentioned by the patient
3. Ensure the number is between ${field.min} and ${field.max}
4. Return ONLY the number as an integer
5. If no clear number is mentioned, return ${Math.floor(
        (field.min + field.max) / 2,
      )}

Answer:`
    } else {
      prompt += `Instructions:
1. Find the patient's answer related to "${field.label}"
2. Return the exact answer or closest match
3. Keep the response concise and factual
4. If no answer is found, return ""

Answer:`
    }

    return prompt
  }

  processAnswerByFieldType(field, answer) {
    if (!answer || answer.trim() === '') {
      return this.getDefaultValueByFieldType(field)
    }

    const cleanAnswer = answer.toString().trim()

    if (field.type === 'radio' && field.options) {
      const lowerAnswer = cleanAnswer.toLowerCase()

      // First, try exact match
      for (const option of field.options) {
        if (option.toLowerCase() === lowerAnswer) {
          return option
        }
      }

      // Then try partial match
      for (const option of field.options) {
        if (
          lowerAnswer.includes(option.toLowerCase()) ||
          option.toLowerCase().includes(lowerAnswer)
        ) {
          return option
        }
      }

      if (field.allowOtherSpecify && field.options.includes('Other')) {
        return 'Other'
      }

      return field.options[0]
    } else if (field.type === 'slider') {
      const numberMatch = cleanAnswer.match(/\d+/)
      if (numberMatch) {
        let value = parseInt(numberMatch[0])
        value = Math.max(field.min || 0, Math.min(field.max || 10, value))
        return value
      }

      return Math.floor(((field.min || 0) + (field.max || 10)) / 2)
    } else {
      return cleanAnswer
    }
  }

  getDefaultValueByFieldType(field) {
    if (field.type === 'radio' && field.options && field.options.length > 0) {
      return field.options[0]
    } else if (field.type === 'slider') {
      return Math.floor(((field.min || 0) + (field.max || 10)) / 2)
    } else {
      return ''
    }
  }

  sanitizeFieldKey(label) {
    return label
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') 
      .replace(/\s+/g, '') 
      .trim()
  }

  async processLifestyleAmbientListening(transcript, source) {
    try {
      logging.logInfo(
        `Processing lifestyle ambient listening for source: ${source}`,
      )

      const conversation = await this.identifySpeaker(transcript)
      const objConversation = helper.parseJSON(conversation)

      if (!objConversation || !Array.isArray(objConversation)) {
        logging.logError('Failed to parse conversation')
        return {
          conversation: [],
          summary: {},
        }
      }

      const summary = await this.processLifestyleQuestions(conversation, source)

      return {
        conversation: objConversation,
        summary: summary,
      }
    } catch (error) {
      logging.logError('Unable to process lifestyle ambient listening', error)
      return {
        conversation: [],
        summary: {},
      }
    }
  }
}

module.exports = new LifestyleAmbientHandler()
