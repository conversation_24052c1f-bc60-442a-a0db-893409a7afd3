const Razorpay = require('razorpay')
const crypto = require('crypto')
const { v4: uuidv4 } = require('uuid')
const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const PaymentModel = require('../models/payment-model')
const { PaymentStatus, PaymentType } = require('../common/constant')
const logging = require('../common/logging')

const paymentsContainer = 'Payments'

class PaymentService {
  constructor() {
    // Initialize Razorpay instance only when needed
    this.razorpay = null
    this.webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET
  }

  // Lazy initialization of Razorpay
  getRazorpayInstance() {
    if (!this.razorpay) {
      if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
        throw new Error('Razorpay credentials not configured')
      }
      this.razorpay = new Razorpay({
        key_id: process.env.RAZORPAY_KEY_ID,
        key_secret: process.env.RAZORPAY_KEY_SECRET,
      })
    }
    return this.razorpay
  }

  /**
   * Create a Razorpay order and save payment record
   * @param {Object} paymentData - Payment data
   * @returns {Object} - Order details with payment ID
   */
  async createOrder(paymentData) {
    try {
      // Validate payment data
      const payment = new PaymentModel(paymentData)

      // Create Razorpay order
      const orderOptions = {
        amount: payment.amount, // Amount in paise
        currency: payment.currency,
        receipt: `receipt_${Date.now()}`,
        notes: {
          paymentType: payment.paymentType,
          patientId: payment.patientId,
          organizationId: payment.organizationId,
          description: payment.description,
          ...payment.metadata,
        },
      }

      const razorpayOrder = await this.getRazorpayInstance().orders.create(
        orderOptions,
      )

      // Update payment with Razorpay order ID
      payment.razorpayOrderId = razorpayOrder.id
      payment.id = uuidv4()

      // Save payment record to database
      const savedPayment = await cosmosDbContext.createItem(
        payment,
        paymentsContainer,
      )

      return {
        success: true,
        data: {
          orderId: razorpayOrder.id,
          paymentId: savedPayment.id,
          keyId: process.env.RAZORPAY_KEY_ID,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          description: payment.description,
        },
      }
    } catch (error) {
      logging.logError('Error creating payment order', error)
      throw new Error(`Failed to create payment order: ${error.message}`)
    }
  }

  /**
   * Verify Razorpay payment signature
   * @param {Object} verificationData - Razorpay verification data
   * @returns {Object} - Verification result
   */
  async verifyPayment(verificationData) {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
        verificationData

      // Generate expected signature
      const body = razorpay_order_id + '|' + razorpay_payment_id
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex')

      const isSignatureValid = expectedSignature === razorpay_signature

      if (isSignatureValid) {
        // Find payment record by Razorpay order ID
        const query = `SELECT * FROM c WHERE c.razorpayOrderId = '${razorpay_order_id}'`
        const payments = await cosmosDbContext.queryItems(
          query,
          paymentsContainer,
        )

        if (payments.length === 0) {
          throw new Error('Payment record not found')
        }

        const payment = payments[0]

        // Update payment status
        payment.status = PaymentStatus.COMPLETED
        payment.razorpayPaymentId = razorpay_payment_id
        payment.razorpaySignature = razorpay_signature
        payment.verifiedAt = new Date().toISOString()

        await cosmosDbContext.updateItem(payment, paymentsContainer)

        return {
          success: true,
          verified: true,
          message: 'Payment verified successfully',
          paymentId: razorpay_payment_id,
          payment: payment,
        }
      } else {
        return {
          success: false,
          verified: false,
          message: 'Invalid payment signature',
        }
      }
    } catch (error) {
      logging.logError('Error verifying payment', error)
      throw new Error(`Payment verification failed: ${error.message}`)
    }
  }

  /**
   * Handle Razorpay webhook events
   * @param {Object} webhookData - Webhook payload
   * @param {String} signature - Webhook signature
   * @returns {Object} - Processing result
   */
  async handleWebhook(webhookData, signature) {
    try {
      // Verify webhook signature
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(JSON.stringify(webhookData))
        .digest('hex')

      if (expectedSignature !== signature) {
        throw new Error('Invalid webhook signature')
      }

      const { event, payload } = webhookData

      // Razorpay sends payment data under payload.payment.entity
      const paymentEntity = payload.payment.entity

      // Extract necessary details
      const orderId = paymentEntity.order_id
      const paymentId = paymentEntity.id
      const paymentStatus = paymentEntity.status
      const errorDescription = paymentEntity.error_description || null

      // Find payment record in DB using orderId
      const query = `SELECT * FROM c WHERE c.razorpayOrderId = '${orderId}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      if (payments.length === 0) {
        logging.logError(`Payment record not found for order ID: ${orderId}`)
        return { success: false, message: 'Payment record not found' }
      }

      const paymentRecord = payments[0]

      // Handle different webhook events
      switch (event) {
        case 'payment.captured':
          paymentRecord.status = PaymentStatus.COMPLETED
          paymentRecord.razorpayPaymentId = paymentId
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        case 'payment.failed':
          paymentRecord.status = PaymentStatus.FAILED
          paymentRecord.failureReason = errorDescription || 'Payment failed'
          paymentRecord.razorpayPaymentId = paymentId
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        default:
          logging.logInfo(`Unhandled webhook event: ${event}`)
          return { success: true, message: 'Event not processed' }
      }

      // Update payment record in Cosmos DB
      await cosmosDbContext.updateItem(paymentRecord, paymentsContainer)

      return {
        success: true,
        message: `Webhook processed for event: ${event}`,
        paymentId: paymentRecord.id,
      }
    } catch (error) {
      logging.logError('Error handling webhook', error)
      throw new Error(`Webhook processing failed: ${error.message}`)
    }
  }

  /**
   * Get payment details by ID
   * @param {String} paymentId - Payment ID
   * @returns {Object} - Payment details
   */
  async getPaymentById(id) {
    try {
      const query = `SELECT * FROM c WHERE c.id = '${id}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      if (payments.length === 0) {
        throw new Error('Payment not found')
      }

      return payments[0]
    } catch (error) {
      logging.logError('Error fetching payment details', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  /**
   * Get payments by organization with pagination
   * @param {String} organizationId - Organization ID
   * @param {Number} pageSize - Page size
   * @param {String} continuationToken - Continuation token
   * @returns {Object} - Paginated payments
   */
  // async getPaymentsByOrganization(
  //   organizationId,
  //   pageSize = 20,
  //   continuationToken = null,
  //   includeTotalCount = false,
  // ) {
  //   try {
  //     let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}'`

  //     // Handle cursor-based fallback tokens
  //     if (continuationToken && continuationToken.startsWith('cursor_')) {
  //       const cursorValue = continuationToken.replace('cursor_', '')
  //       if (cursorValue.includes('T')) {
  //         // ISO timestamp cursor
  //         query += ` AND c.createdAt < '${cursorValue}'`
  //       } else {
  //         // _ts cursor
  //         query += ` AND c._ts < ${cursorValue}`
  //       }
  //     }

  //     query += ` ORDER BY c.createdAt DESC`

  //     const result = await cosmosDbContext.getAllItemQueryWithPagination(
  //       paymentsContainer,
  //       query,
  //       pageSize,
  //       continuationToken && !continuationToken.startsWith('cursor_')
  //         ? continuationToken
  //         : null,
  //     )

  //     const response = {
  //       payments: result.items || [],
  //       continuationToken: result.nextToken || result.continuationToken,
  //       hasMoreResults:
  //         result.hasMoreResults ||
  //         !!(result.nextToken || result.continuationToken),
  //       pageSize: pageSize,
  //       itemCount: result.items ? result.items.length : 0,
  //     }

  //     // Only fetch total count when explicitly requested (e.g., for dashboard stats)
  //     if (includeTotalCount) {
  //       const countQuery = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}'`
  //       const countResult = await cosmosDbContext.queryItems(
  //         countQuery,
  //         paymentsContainer,
  //       )
  //       response.totalCount = countResult[0] || 0
  //     }

  //     return response
  //   } catch (error) {
  //     logging.logError('Error fetching organization payments', error)
  //     throw new Error(`Failed to fetch payments: ${error.message}`)
  //   }
  // }
  async getPaymentsByOrganization(
    organizationId,
    pageSize = 20,
    continuationToken = null,
    includeTotalCount = false,
    filters = {}, // <-- Add filters parameter
  ) {
    try {
      let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}'`

      // Add dynamic filters
      if (filters.patientId) {
        query += ` AND c.patientId = '${filters.patientId}'`
      }

      if (filters.status) {
        query += ` AND c.status = '${filters.status}'`
      }

      if (filters.paymentType) {
        query += ` AND c.paymentType = '${filters.paymentType}'`
      }

      if (filters.startDate) {
        query += ` AND c.createdAt >= '${filters.startDate}'`
      }

      if (filters.endDate) {
        query += ` AND c.createdAt <= '${filters.endDate}'`
      }

      if (filters.minAmount !== null && filters.minAmount !== undefined) {
        query += ` AND c.amount >= ${filters.minAmount}`
      }

      if (filters.maxAmount !== null && filters.maxAmount !== undefined) {
        query += ` AND c.amount <= ${filters.maxAmount}`
      }

      // Handle cursor-based fallback tokens
      if (continuationToken && continuationToken.startsWith('cursor_')) {
        const cursorValue = continuationToken.replace('cursor_', '')
        if (cursorValue.includes('T')) {
          query += ` AND c.createdAt < '${cursorValue}'`
        } else {
          query += ` AND c._ts < ${cursorValue}`
        }
      }

      query += ` ORDER BY c.createdAt DESC`

      const result = await cosmosDbContext.getAllItemQueryWithPagination(
        paymentsContainer,
        query,
        pageSize,
        continuationToken && !continuationToken.startsWith('cursor_')
          ? continuationToken
          : null,
      )

      const response = {
        payments: result.items || [],
        continuationToken: result.nextToken || result.continuationToken,
        hasMoreResults:
          result.hasMoreResults ||
          !!(result.nextToken || result.continuationToken),
        pageSize: pageSize,
        itemCount: result.items ? result.items.length : 0,
      }

      // Only fetch total count when explicitly requested
      if (includeTotalCount) {
        let countQuery = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}'`

        // Reuse same filter logic for count query
        if (filters.patientId) {
          countQuery += ` AND c.patientId = '${filters.patientId}'`
        }

        if (filters.status) {
          countQuery += ` AND c.status = '${filters.status}'`
        }

        if (filters.paymentType) {
          countQuery += ` AND c.paymentType = '${filters.paymentType}'`
        }

        if (filters.startDate) {
          countQuery += ` AND c.createdAt >= '${filters.startDate}'`
        }

        if (filters.endDate) {
          countQuery += ` AND c.createdAt <= '${filters.endDate}'`
        }

        if (filters.minAmount !== null && filters.minAmount !== undefined) {
          countQuery += ` AND c.amount >= ${filters.minAmount}`
        }

        if (filters.maxAmount !== null && filters.maxAmount !== undefined) {
          countQuery += ` AND c.amount <= ${filters.maxAmount}`
        }

        const countResult = await cosmosDbContext.queryItems(
          countQuery,
          paymentsContainer,
        )
        response.totalCount = countResult[0] || 0
      }

      return response
    } catch (error) {
      logging.logError('Error fetching organization payments', error)
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  /**
   * Get payments by organization using cursor-based pagination (alternative approach)
   * More efficient for large datasets with frequent updates
   * @param {String} organizationId - Organization ID
   * @param {Number} pageSize - Page size
   * @param {String} cursor - Cursor (timestamp) for pagination
   * @returns {Object} - Paginated payments with cursor
   */
  async getPaymentsByOrganizationCursor(
    organizationId,
    pageSize = 20,
    cursor = null,
  ) {
    try {
      let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}'`

      if (cursor) {
        query += ` AND c.createdAt < '${cursor}'`
      }

      query += ` ORDER BY c.createdAt DESC OFFSET 0 LIMIT ${pageSize + 1}`

      const result = await cosmosDbContext.queryItems(query, paymentsContainer)

      const hasMoreResults = result.length > pageSize
      const payments = hasMoreResults ? result.slice(0, pageSize) : result
      const nextCursor = hasMoreResults
        ? payments[payments.length - 1].createdAt
        : null

      return {
        payments,
        nextCursor,
        hasMoreResults,
        pageSize,
        itemCount: payments.length,
      }
    } catch (error) {
      logging.logError(
        'Error fetching organization payments with cursor',
        error,
      )
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  /**
   * Get payment statistics for an organization
   * @param {String} organizationId - Organization ID
   * @returns {Object} - Payment statistics
   */
  async getPaymentStatistics(organizationId) {
    try {
      // Get all completed payments for the organization
      const query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status = '${PaymentStatus.COMPLETED}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      // Calculate statistics
      const totalAmount = payments.reduce(
        (sum, payment) => sum + payment.amount,
        0,
      )
      const totalCount = payments.length

      // Group by payment type
      const paymentTypeStats = {}
      payments.forEach((payment) => {
        if (!paymentTypeStats[payment.paymentType]) {
          paymentTypeStats[payment.paymentType] = {
            count: 0,
            amount: 0,
          }
        }
        paymentTypeStats[payment.paymentType].count++
        paymentTypeStats[payment.paymentType].amount += payment.amount
      })

      // Group by month
      const monthlyStats = {}
      payments.forEach((payment) => {
        const month = new Date(payment.createdAt).toISOString().substring(0, 7) // YYYY-MM
        if (!monthlyStats[month]) {
          monthlyStats[month] = {
            count: 0,
            amount: 0,
          }
        }
        monthlyStats[month].count++
        monthlyStats[month].amount += payment.amount
      })

      return {
        totalAmount: totalAmount / 100, // Convert to rupees
        totalCount,
        paymentTypeBreakdown: Object.keys(paymentTypeStats).map((type) => ({
          type,
          count: paymentTypeStats[type].count,
          amount: paymentTypeStats[type].amount / 100, // Convert to rupees
        })),
        monthlyBreakdown: Object.keys(monthlyStats)
          .map((month) => ({
            month,
            count: monthlyStats[month].count,
            amount: monthlyStats[month].amount / 100, // Convert to rupees
          }))
          .sort((a, b) => a.month.localeCompare(b.month)),
      }
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  /**
   * Update payment status (for manual updates)
   * @param {String} paymentId - Payment ID
   * @param {String} status - New status
   * @param {String} reason - Reason for status change
   * @returns {Object} - Updated payment
   */
  async updatePaymentStatus(paymentId, status, reason = null) {
    try {
      const payment = await this.getPaymentById(paymentId)

      payment.status = status
      if (reason) {
        payment.failureReason = reason
      }

      const updatedPayment = await cosmosDbContext.updateItem(
        payment,
        paymentsContainer,
      )
      return updatedPayment
    } catch (error) {
      logging.logError('Error updating payment status', error)
      throw new Error(`Failed to update payment status: ${error.message}`)
    }
  }
}

module.exports = new PaymentService()
