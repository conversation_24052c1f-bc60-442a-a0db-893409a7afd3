# Razorpay Payment Integration Documentation

## Overview

This document provides comprehensive documentation for the Razorpay payment integration in the EMR system. The integration supports four types of payments: patient registration fees, consultation fees, prescription medicine costs, and lab test costs.

## Table of Contents

1. [Payment Types](#payment-types)
2. [API Endpoints](#api-endpoints)
3. [Payment Flow](#payment-flow)
4. [Test Scenarios](#test-scenarios)
5. [Sample Requests/Responses](#sample-requestsresponses)
6. [<PERSON><PERSON><PERSON> Handling](#error-handling)
7. [Webhook Configuration](#webhook-configuration)

## Payment Types

### 1. Patient Registration Fee
- **Trigger**: Patient registration when organization has `registrationFee > 0`
- **Payment Type**: `patient_registration`
- **Amount Source**: Organization's `registrationFee` field

### 2. Doctor Consultation Fee
- **Trigger**: Appointment booking when doctor has `consultationFee > 0`
- **Payment Type**: `consultation`
- **Amount Source**: Doctor's `consultationFee` field

### 3. Prescription Medicine Cost
- **Trigger**: Prescription creation when medicines have costs
- **Payment Type**: `prescription`
- **Amount Source**: Sum of (medicine.cost × medicine.quantity)

### 4. Lab Test Cost
- **Trigger**: Lab test ordering when tests have costs
- **Payment Type**: `lab_test`
- **Amount Source**: Sum of (test.cost × test.qty)

## API Endpoints

### Core Payment APIs

#### 1. Create Payment Order
```
POST /api/payments/create-order
```
Creates a Razorpay order and saves payment record.

#### 2. Verify Payment
```
POST /api/payments/verify
```
Verifies payment signature after Razorpay checkout.

#### 3. Payment Webhook
```
POST /api/payments/webhook
```
Handles Razorpay webhook events (anonymous access).

#### 4. Get Payment Details
```
GET /api/payments/details?paymentId={id}
```
Retrieves payment information by ID.

#### 5. Organization Payments
```
GET /api/payments/organization?organizationId={id}&pageSize=20&continuationToken={token}
```
Lists payments for an organization with pagination.

#### 6. Payment Statistics
```
GET /api/payments/stats?organizationId={id}
```
Returns payment analytics and statistics.

#### 7. Search Payments
```
GET /api/payments/search?organizationId={id}&status=completed&paymentType=consultation
```
Advanced payment filtering and search.

### Integration-Specific APIs

#### Patient Registration with Payment
```
POST /api/patient (with payment integration)
```
Use `CreatePatientProfileWithPayment` method.

#### Appointment Booking with Payment
```
POST /api/appointment (with payment integration)
```
Use `bookAppointmentWithPayment` method.

#### Prescription with Payment
```
POST /api/prescriptions (with payment integration)
```
Use `createPrescriptionWithPayment` method.

#### Lab Test with Payment
```
POST /api/patient-lab-test (with payment integration)
```
Use `createLabTestWithPayment` method.

## Payment Flow

### Standard Payment Flow

1. **Create Order**: Frontend calls create-order API
2. **Razorpay Checkout**: Frontend opens Razorpay checkout with order details
3. **Payment Processing**: User completes payment on Razorpay
4. **Verification**: Frontend calls verify API with payment details
5. **Completion**: Backend completes the original action (registration, booking, etc.)

### Webhook Backup Flow

1. **Webhook Event**: Razorpay sends webhook to `/api/payments/webhook`
2. **Status Update**: Backend updates payment status based on webhook
3. **Fallback Processing**: Acts as backup when frontend flow fails

## Test Scenarios

### 1. Patient Registration with Fee

**Test Flow:**
```
1. Create organization with registrationFee = 200
2. POST /api/patient with patient data
3. Response includes requiresPayment: true and paymentOrder details
4. Use paymentOrder.orderId for Razorpay checkout
5. After payment, POST /api/payments/verify
6. Complete registration with patient data and paymentId
```

### 2. Consultation Booking with Fee

**Test Flow:**
```
1. Set doctor consultationFee = 500
2. POST appointment data to booking API
3. Response includes requiresPayment: true and paymentOrder details
4. Complete Razorpay payment
5. Verify payment and complete appointment booking
```

### 3. Prescription with Medicine Costs

**Test Flow:**
```
1. Create prescription with medicines having cost values
2. POST prescription data with requirePayment: true
3. System calculates total medicine cost
4. Complete payment flow
5. Create prescription after payment verification
```

### 4. Lab Test with Costs

**Test Flow:**
```
1. Create lab test order with tests having cost values
2. POST lab test data with requirePayment: true
3. System calculates total test cost
4. Complete payment flow
5. Create lab test order after payment verification
```

## Sample Requests/Responses

### Create Payment Order Request
```json
{
  "amount": 200,
  "currency": "INR",
  "paymentType": "patient_registration",
  "patientId": "patient-123",
  "organizationId": "org-456",
  "description": "Registration Fee for John Doe",
  "metadata": {
    "patientName": "John Doe",
    "organizationName": "City Hospital"
  }
}
```

### Create Payment Order Response
```json
{
  "success": true,
  "data": {
    "orderId": "order_xyz123",
    "paymentId": "internal-payment-id",
    "keyId": "rzp_test_CUHJLaG7X7H8uG",
    "amount": 20000,
    "currency": "INR",
    "status": "created",
    "description": "Registration Fee for John Doe"
  }
}
```

### Verify Payment Request
```json
{
  "razorpay_order_id": "order_xyz123",
  "razorpay_payment_id": "pay_abc456",
  "razorpay_signature": "generated_signature_hash"
}
```

### Verify Payment Response
```json
{
  "success": true,
  "verified": true,
  "message": "Payment verified successfully",
  "paymentId": "pay_abc456",
  "payment": {
    "id": "internal-payment-id",
    "status": "completed",
    "amount": 20000,
    "paymentType": "patient_registration"
  }
}
```

## Error Handling

### Common Error Responses

#### Invalid Payment Data
```json
{
  "error": "Missing required fields: amount, paymentType",
  "statusCode": 400
}
```

#### Payment Verification Failed
```json
{
  "success": false,
  "verified": false,
  "message": "Invalid payment signature"
}
```

#### Payment Not Found
```json
{
  "error": "Payment not found",
  "statusCode": 404
}
```

## Webhook Configuration

### Webhook URL
```
POST https://your-domain.com/api/payments/webhook
```

### Required Events
- `payment.captured`
- `payment.failed`

### Webhook Secret
Set `RAZORPAY_WEBHOOK_SECRET` in environment variables.

### Sample Webhook Payload
```json
{
  "event": "payment.captured",
  "payload": {
    "payment": {
      "id": "pay_abc456",
      "status": "captured",
      "amount": 20000
    },
    "order": {
      "id": "order_xyz123"
    }
  }
}
```

## Environment Configuration

### Required Environment Variables
```
RAZORPAY_KEY_ID=rzp_test_CUHJLaG7X7H8uG
RAZORPAY_KEY_SECRET=f3BsrkGkuYlWZ5boY3k6yXJ6
RAZORPAY_WEBHOOK_SECRET=razorpay_webhook_secret_test
```

## Database Schema

### Payment Record
```json
{
  "id": "payment-id",
  "razorpayOrderId": "order_xyz123",
  "razorpayPaymentId": "pay_abc456",
  "razorpaySignature": "signature_hash",
  "amount": 20000,
  "currency": "INR",
  "status": "completed",
  "paymentType": "patient_registration",
  "patientId": "patient-123",
  "organizationId": "org-456",
  "description": "Registration Fee for John Doe",
  "createdAt": "2025-01-01T00:00:00.000Z",
  "verifiedAt": "2025-01-01T00:01:00.000Z",
  "metadata": {
    "patientName": "John Doe",
    "organizationName": "City Hospital"
  }
}
```

## Testing Checklist

- [ ] Patient registration with fee
- [ ] Patient registration without fee
- [ ] Consultation booking with fee
- [ ] Consultation booking without fee
- [ ] Prescription with medicine costs
- [ ] Prescription without costs
- [ ] Lab test with costs
- [ ] Lab test without costs
- [ ] Payment verification success
- [ ] Payment verification failure
- [ ] Webhook processing
- [ ] Payment statistics
- [ ] Error handling scenarios
