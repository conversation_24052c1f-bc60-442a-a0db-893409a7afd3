const { app } = require('@azure/functions');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const lifestyleAmbientHandler = require('../handlers/lifestyle-ambient-handler');

app.http('lifestyle-ambient-listening', {
    methods: ['POST'],
    authLevel: 'anonymous',
    route: 'lifestyle/ambient-listening',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);

        try {
            // Parse request body
            const body = await req.json();
            const { transcript, source } = body;

            // Validate required parameters
            if (!transcript || transcript.trim() === '') {
                return jsonResponse('Missing or empty transcript', HttpStatusCode.BadRequest);
            }

            if (!source || source.trim() === '') {
                return jsonResponse('Missing or empty source', HttpStatusCode.BadRequest);
            }

            context.log(`Processing lifestyle ambient listening for source: ${source}`);

            // Process the lifestyle ambient listening
            const result = await lifestyleAmbientHandler.processLifestyleAmbientListening(transcript, source);

            // Validate result
            if (!result) {
                return jsonResponse('Failed to process lifestyle ambient listening', HttpStatusCode.InternalServerError);
            }

            // Return the structured response similar to summary API
            return jsonResponse({
                conversation: result.conversation || [],
                summary: result.summary || {}
            }, HttpStatusCode.Ok);

        } catch (error) {
            context.log('Error in lifestyle ambient listening handler:', error);
            
            // Handle JSON parsing errors
            if (error.name === 'SyntaxError') {
                return jsonResponse('Invalid JSON in request body', HttpStatusCode.BadRequest);
            }
            
            return jsonResponse('Internal server error', HttpStatusCode.InternalServerError);
        }
    }
});
