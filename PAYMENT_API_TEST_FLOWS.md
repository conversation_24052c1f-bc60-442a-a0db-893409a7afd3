# Payment API Test Flows

## Prerequisites

1. **Environment Setup**
   - Razorpay test credentials configured in `local.settings.json`
   - Azure Functions running locally or deployed
   - Organization with `registrationFee > 0` created
   - Doctor with `consultationFee > 0` created

2. **Test Credentials**
   ```
   RAZORPAY_KEY_ID=rzp_test_CUHJLaG7X7H8uG
   RAZORPAY_KEY_SECRET=f3BsrkGkuYlWZ5boY3k6yXJ6
   ```

## Test Flow 1: Patient Registration with Payment

### Step 1: Create Payment Order
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 200,
    "currency": "INR",
    "paymentType": "patient_registration",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Registration Fee for <PERSON>",
    "metadata": {
      "patientName": "John Doe",
      "organizationName": "Test Hospital"
    }
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_xyz123",
    "paymentId": "internal-payment-id",
    "keyId": "rzp_test_CUHJLaG7X7H8uG",
    "amount": 20000,
    "currency": "INR",
    "status": "created"
  }
}
```

### Step 2: Simulate Payment Verification
```bash
curl -X POST "http://localhost:7071/api/payments/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "razorpay_order_id": "order_xyz123",
    "razorpay_payment_id": "pay_test123",
    "razorpay_signature": "test_signature_hash"
  }'
```

### Step 3: Complete Patient Registration
```bash
curl -X POST "http://localhost:7071/api/patient/complete-registration" \
  -H "Content-Type: application/json" \
  -d '{
    "patientData": {
      "id": "test-patient-001",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "paymentId": "internal-payment-id"
  }'
```

## Test Flow 2: Consultation Booking with Payment

### Step 1: Get Consultation Fee
```bash
curl -X GET "http://localhost:7071/api/appointments/consultation-fee?doctorId=test-doctor-001"
```

### Step 2: Create Payment Order for Consultation
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 500,
    "currency": "INR",
    "paymentType": "consultation",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Consultation Fee for Dr. Smith",
    "metadata": {
      "appointmentId": "test-apt-001",
      "doctorId": "test-doctor-001",
      "doctorName": "Dr. Smith",
      "patientName": "John Doe",
      "appointmentDate": "2025-01-15",
      "appointmentTime": "10:00 AM"
    }
  }'
```

### Step 3: Complete Appointment Booking
```bash
curl -X POST "http://localhost:7071/api/appointments/complete-booking" \
  -H "Content-Type: application/json" \
  -d '{
    "appointmentData": {
      "id": "test-apt-001",
      "patientId": "test-patient-001",
      "doctorId": "test-doctor-001",
      "date": "2025-01-15",
      "time": "10:00 AM"
    },
    "paymentId": "consultation-payment-id"
  }'
```

## Test Flow 3: Prescription with Payment

### Step 1: Get Prescription Cost Estimate
```bash
curl -X POST "http://localhost:7071/api/prescriptions/cost-estimate" \
  -H "Content-Type: application/json" \
  -d '{
    "medicines": [
      {
        "brandName": "Paracetamol",
        "cost": 50,
        "quantity": 2
      },
      {
        "brandName": "Amoxicillin",
        "cost": 120,
        "quantity": 1
      }
    ]
  }'
```

### Step 2: Create Payment Order for Prescription
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 220,
    "currency": "INR",
    "paymentType": "prescription",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Prescription Medicines",
    "metadata": {
      "prescriptionId": "test-presc-001",
      "patientName": "John Doe",
      "medicineCount": 2,
      "totalCost": 220
    }
  }'
```

### Step 3: Complete Prescription Creation
```bash
curl -X POST "http://localhost:7071/api/prescriptions/complete-creation" \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "test-patient-001",
    "medicines": [
      {
        "brandName": "Paracetamol",
        "cost": 50,
        "quantity": 2
      }
    ],
    "doctor": "Dr. Smith",
    "paymentId": "prescription-payment-id",
    "prescriptionId": "test-presc-001"
  }'
```

## Test Flow 4: Lab Test with Payment

### Step 1: Get Lab Test Cost Estimate
```bash
curl -X POST "http://localhost:7071/api/patient-lab-test/cost-estimate" \
  -H "Content-Type: application/json" \
  -d '{
    "labTests": [
      {
        "testName": "Blood Test",
        "cost": 300,
        "qty": 1
      },
      {
        "testName": "Urine Test",
        "cost": 150,
        "qty": 1
      }
    ]
  }'
```

### Step 2: Create Payment Order for Lab Tests
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 450,
    "currency": "INR",
    "paymentType": "lab_test",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Lab Tests Payment",
    "metadata": {
      "labTestOrderId": "test-lab-order-001",
      "testIds": ["test-01", "test-02"],
      "testNames": ["Blood Test", "Urine Test"],
      "testCount": 2,
      "totalCost": 450
    }
  }'
```

### Step 3: Complete Lab Test Order
```bash
curl -X POST "http://localhost:7071/api/patient-lab-test/complete-order" \
  -H "Content-Type: application/json" \
  -d '{
    "patientId": "test-patient-001",
    "labTests": [
      {
        "testName": "Blood Test",
        "cost": 300,
        "qty": 1
      }
    ],
    "paymentId": "lab-test-payment-id",
    "labTestOrderId": "test-lab-order-001"
  }'
```

## Test Flow 5: Payment Management APIs

### Get Payment Details
```bash
curl -X GET "http://localhost:7071/api/payments/details?paymentId=internal-payment-id"
```

### Get Organization Payments
```bash
curl -X GET "http://localhost:7071/api/payments/organization?organizationId=test-org-001&pageSize=10"
```

### Get Payment Statistics
```bash
curl -X GET "http://localhost:7071/api/payments/stats?organizationId=test-org-001"
```

### Search Payments
```bash
curl -X GET "http://localhost:7071/api/payments/search?organizationId=test-org-001&status=completed&paymentType=consultation"
```

## Test Flow 6: Webhook Testing

### Simulate Webhook Event
```bash
curl -X POST "http://localhost:7071/api/payments/webhook" \
  -H "Content-Type: application/json" \
  -H "x-razorpay-signature: test_webhook_signature" \
  -d '{
    "event": "payment.captured",
    "payload": {
      "payment": {
        "id": "pay_webhook_test",
        "status": "captured",
        "amount": 20000
      },
      "order": {
        "id": "order_webhook_test"
      }
    }
  }'
```

## Frontend Integration Example

### HTML/JavaScript Example
```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body>
    <button onclick="startPayment()">Pay Now</button>
    
    <script>
    async function startPayment() {
        // Step 1: Create order
        const orderResponse = await fetch('/api/payments/create-order', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                amount: 200,
                paymentType: 'patient_registration',
                patientId: 'test-patient-001',
                organizationId: 'test-org-001',
                description: 'Registration Fee'
            })
        });
        
        const orderData = await orderResponse.json();
        
        // Step 2: Open Razorpay checkout
        const options = {
            key: orderData.data.keyId,
            amount: orderData.data.amount,
            currency: orderData.data.currency,
            order_id: orderData.data.orderId,
            handler: async function(response) {
                // Step 3: Verify payment
                const verifyResponse = await fetch('/api/payments/verify', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        razorpay_order_id: response.razorpay_order_id,
                        razorpay_payment_id: response.razorpay_payment_id,
                        razorpay_signature: response.razorpay_signature
                    })
                });
                
                const verifyData = await verifyResponse.json();
                if (verifyData.verified) {
                    alert('Payment successful!');
                    // Complete the registration/booking process
                } else {
                    alert('Payment verification failed!');
                }
            }
        };
        
        const rzp = new Razorpay(options);
        rzp.open();
    }
    </script>
</body>
</html>
```

## Testing Checklist

- [ ] Patient registration with fee creates payment order
- [ ] Patient registration without fee skips payment
- [ ] Consultation booking with fee creates payment order
- [ ] Consultation booking without fee skips payment
- [ ] Prescription with costs creates payment order
- [ ] Prescription without costs skips payment
- [ ] Lab test with costs creates payment order
- [ ] Lab test without costs skips payment
- [ ] Payment verification works correctly
- [ ] Webhook processing updates payment status
- [ ] Payment statistics are calculated correctly
- [ ] Error handling works for invalid requests
- [ ] All API endpoints return proper HTTP status codes
- [ ] Payment records are stored correctly in database
