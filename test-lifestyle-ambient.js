// Simple test script for lifestyle ambient listening API
// This validates the structure without requiring external services

console.log('='.repeat(50))
console.log('LIFESTYLE AMBIENT LISTENING API STRUCTURE TESTS')
console.log('='.repeat(50))

async function testLifestyleAmbientListening() {
  console.log('Testing Lifestyle Ambient Listening...')

  // Sample transcript for testing
  const sampleTranscript = `
    Doctor: Good morning! How are you feeling today? Let's talk about your lifestyle habits. How often do you exercise?
    Patient: Hi doctor. I try to exercise about 3 times a week, usually at the gym. I do cardio and some weight training.
    Doctor: That's great! What about your diet? Do you follow any specific eating patterns?
    Patient: I try to eat healthy most of the time. I have breakfast, lunch and dinner regularly. I avoid too much junk food but sometimes I do indulge on weekends.
    Doctor: Do you smoke or drink alcohol?
    Patient: I don't smoke at all. I have a glass of wine occasionally, maybe once or twice a week with dinner.
    Doctor: How many hours of sleep do you usually get?
    Patient: I try to get 7-8 hours of sleep every night. Sometimes it's less if I have work stress.
    Doctor: Any stress in your life currently?
    Patient: Yes, work has been quite stressful lately. I'm working on a big project with tight deadlines.
    `

  const source = 'lifestyle-assessment'

  try {
    console.log('Processing transcript...')
    const result =
      await lifestyleAmbientHandler.processLifestyleAmbientListening(
        sampleTranscript,
        source,
      )

    console.log('\n=== RESULT ===')
    console.log('Conversation:', JSON.stringify(result.conversation, null, 2))
    console.log('Summary:', JSON.stringify(result.summary, null, 2))

    // Validate result structure
    if (result.conversation && Array.isArray(result.conversation)) {
      console.log('\n✅ Conversation parsing: PASSED')
    } else {
      console.log('\n❌ Conversation parsing: FAILED')
    }

    if (result.summary && typeof result.summary === 'object') {
      console.log('✅ Summary generation: PASSED')
      console.log(
        `   Generated ${Object.keys(result.summary).length} summary fields`,
      )
    } else {
      console.log('❌ Summary generation: FAILED')
    }
  } catch (error) {
    console.error('Test failed with error:', error)
  }
}

// Test the sanitizeFieldKey function
function testSanitizeFieldKey() {
  console.log('\nTesting sanitizeFieldKey function...')

  const testCases = [
    'Exercise Frequency',
    'Dietary Habits & Preferences',
    'Smoking History (if any)',
    'Sleep Duration per Night',
    'Stress Level (1-10)',
    'Alcohol Consumption',
  ]

  testCases.forEach((testCase) => {
    const result = lifestyleAmbientHandler.sanitizeFieldKey(testCase)
    console.log(`"${testCase}" -> "${result}"`)
  })
}

// Run tests
async function runTests() {
  console.log('='.repeat(50))
  console.log('LIFESTYLE AMBIENT LISTENING API TESTS')
  console.log('='.repeat(50))

  testSanitizeFieldKey()

  console.log('\n' + '='.repeat(50))
  await testLifestyleAmbientListening()

  console.log('\n' + '='.repeat(50))
  console.log('Tests completed!')
}

// Only run if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = {
  testLifestyleAmbientListening,
  testSanitizeFieldKey,
}
