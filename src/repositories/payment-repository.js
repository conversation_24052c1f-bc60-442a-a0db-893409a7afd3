const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')

const paymentsContainer = 'Payments'

class PaymentRepository {
  /**
   * Create a new payment record
   * @param {Object} payment - Payment data
   * @returns {Object} - Created payment
   */
  async createPayment(payment) {
    try {
      return await cosmosDbContext.createItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error creating payment record', error)
      throw new Error(`Failed to create payment: ${error.message}`)
    }
  }

  /**
   * Update payment record
   * @param {Object} payment - Payment data
   * @returns {Object} - Updated payment
   */
  async updatePayment(payment) {
    try {
      return await cosmosDbContext.updateItem(payment, paymentsContainer)
    } catch (error) {
      logging.logError('Error updating payment record', error)
      throw new Error(`Failed to update payment: ${error.message}`)
    }
  }
  /**
   * Get payment by ID
   * @param {String} paymentId - Payment ID
   * @returns {Object} - Payment record
   */
  async getPaymentById(paymentId) {
    try {
      const query = `SELECT * FROM c WHERE c.id = '${paymentId}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )
      return payments.length > 0 ? payments[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by ID', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  /**
   * Get payment by Razorpay order ID
   * @param {String} razorpayOrderId - Razorpay order ID
   * @returns {Object} - Payment record
   */
  async getPaymentByRazorpayOrderId(razorpayOrderId) {
    try {
      const query = `SELECT * FROM c WHERE c.razorpayOrderId = '${razorpayOrderId}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )
      return payments.length > 0 ? payments[0] : null
    } catch (error) {
      logging.logError('Error fetching payment by Razorpay order ID', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  /**
   * Get payments by organization ID with optional filters and pagination
   * @param {String} organizationId - Organization ID (optional - if null, gets all payments)
   * @param {Number} pageSize - Page size
   * @param {String} continuationToken - Continuation token
   * @param {Object} filters - Additional filters
   * @returns {Object} - Paginated payment records
   */
  async getPaymentsByOrganization(
    organizationId = null,
    pageSize = 20,
    continuationToken = null,
    filters = {},
  ) {
    try {
      let query = 'SELECT * FROM c WHERE 1=1'
      console.log(query, 'kkkkkkkkkkkkkkkkkkk')

      if (organizationId) {
        query += ` AND c.organizationId = '${organizationId}'`
      }

      if (filters.patientId) {
        query += ` AND c.patientId = '${filters.patientId}'`
      }
      if (filters.status) {
        query += ` AND c.status = '${filters.status}'`
      }
      if (filters.paymentType) {
        query += ` AND c.paymentType = '${filters.paymentType}'`
      }
      if (filters.startDate) {
        query += ` AND c.createdAt >= '${filters.startDate}'`
      }
      if (filters.endDate) {
        query += ` AND c.createdAt <= '${filters.endDate}'`
      }
      if (filters.minAmount) {
        query += ` AND c.amount >= ${filters.minAmount}`
      }
      if (filters.maxAmount) {
        query += ` AND c.amount <= ${filters.maxAmount}`
      }

      query += ' ORDER BY c.createdAt DESC'

      return await cosmosDbContext.getAllItemQueryWithPagination(
        paymentsContainer,
        query,
        pageSize,
        continuationToken,
      )
    } catch (error) {
      logging.logError('Error fetching payments by organization', error)
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  /**
   * Get payment statistics for organization
   * @param {String} organizationId - Organization ID (optional)
   * @param {String} startDate - Start date filter (optional)
   * @param {String} endDate - End date filter (optional)
   * @returns {Object} - Payment statistics
   */
  async getPaymentStatistics(
    organizationId = null,
    startDate = null,
    endDate = null,
  ) {
    try {
      let query = 'SELECT c.status, c.paymentType, c.amount FROM c WHERE 1=1'

      if (organizationId) {
        query += ` AND c.organizationId = '${organizationId}'`
      }
      if (startDate) {
        query += ` AND c.createdAt >= '${startDate}'`
      }
      if (endDate) {
        query += ` AND c.createdAt <= '${endDate}'`
      }

      return await cosmosDbContext.queryItems(query, paymentsContainer)
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }

  /**
   * Search payments with text search
   * @param {String} searchText - Search text
   * @param {String} organizationId - Organization ID (optional)
   * @param {Number} pageSize - Page size
   * @param {String} continuationToken - Continuation token
   * @returns {Object} - Search results
   */
  async searchPayments(
    searchText,
    organizationId = null,
    pageSize = 20,
    continuationToken = null,
  ) {
    try {
      let query = `SELECT * FROM c WHERE (
      CONTAINS(LOWER(c.description), LOWER('${searchText}')) OR
      CONTAINS(LOWER(c.patientId), LOWER('${searchText}')) OR
      CONTAINS(LOWER(c.razorpayOrderId), LOWER('${searchText}')) OR
      CONTAINS(LOWER(c.razorpayPaymentId), LOWER('${searchText}'))
    )`

      if (organizationId) {
        query += ` AND c.organizationId = '${organizationId}'`
      }

      query += ' ORDER BY c.createdAt DESC'

      const options = {
        maxItemCount: pageSize,
      }

      if (continuationToken) {
        options.continuationToken = continuationToken
      }

      return await cosmosDbContext.getAllItemQueryWithPagination(
        query,
        paymentsContainer,
        options,
      )
    } catch (error) {
      logging.logError('Error searching payments', error)
      throw new Error(`Failed to search payments: ${error.message}`)
    }
  }
}

module.exports = new PaymentRepository()
