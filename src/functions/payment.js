const { app } = require('@azure/functions')
const paymentHandler = require('../handlers/payment-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { HttpMethod } = require('../common/constant')

/**
 * Create Payment Order
 * POST /api/payments/create-order
 * 
 * Test Flow:
 * 1. Patient Registration: POST with paymentType="patient_registration", amount=200, organizationId, patientId
 * 2. Consultation: POST with paymentType="consultation", amount=doctor.consultationFee, metadata.appointmentId, metadata.doctorId
 * 3. Prescription: POST with paymentType="prescription", amount=totalMedicineCost, metadata.prescriptionId
 * 4. Lab Test: POST with paymentType="lab_test", amount=labTestTotal, metadata.testIds=["test-01", "test-02"]
 */
app.http('create-payment-order', {
  methods: ['POST'],
  route: 'payments/create-order',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Creating payment order')
    return await paymentHandler.createOrder(req)
  }
})

/**
 * Verify Payment
 * POST /api/payments/verify
 * 
 * Test Flow:
 * 1. After Razorpay checkout success, frontend calls this with razorpay_order_id, razorpay_payment_id, razorpay_signature
 * 2. This verifies the signature and marks payment as completed
 * 3. Returns success/failure status for frontend to proceed with next steps
 */
app.http('verify-payment', {
  methods: ['POST'],
  route: 'payments/verify',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Verifying payment')
    return await paymentHandler.verifyPayment(req)
  }
})

/**
 * Razorpay Webhook Handler
 * POST /api/payments/webhook
 * 
 * Test Flow:
 * 1. Razorpay server sends webhook events (payment.captured, payment.failed)
 * 2. This endpoint processes the webhook and updates payment status
 * 3. Acts as backup when frontend verification flow is interrupted
 */
app.http('payment-webhook', {
  methods: ['POST'],
  route: 'payments/webhook',
  authLevel: 'anonymous', // Webhooks come from Razorpay, not authenticated users
  handler: async (req, context) => {
    context.log('Processing payment webhook')
    return await paymentHandler.handleWebhook(req)
  }
})

/**
 * Get Payment Details
 * GET /api/payments/details?paymentId=xxx
 * 
 * Test Flow:
 * 1. Frontend/backend can check payment status using payment ID
 * 2. Returns complete payment information including status, amounts, metadata
 */
app.http('payment-details', {
  methods: ['GET'],
  route: 'payments/details',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching payment details')
    return await paymentHandler.getPaymentDetails(req)
  }
})

/**
 * Get Organization Payments (Paginated)
 * GET /api/payments/organization?organizationId=xxx&pageSize=20&continuationToken=xxx
 * 
 * Test Flow:
 * 1. Organization admin can view all payments for their organization
 * 2. Supports pagination for large datasets
 * 3. Returns payment history with metadata
 */
app.http('organization-payments', {
  methods: ['GET'],
  route: 'payments/organization',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching organization payments')
    return await paymentHandler.getOrganizationPayments(req)
  }
})

/**
 * Get Payment Statistics
 * GET /api/payments/stats?organizationId=xxx
 * 
 * Test Flow:
 * 1. Organization admin can view payment analytics
 * 2. Returns total amounts, counts by payment type, monthly breakdown
 * 3. Only includes completed payments for accurate reporting
 */
app.http('payment-statistics', {
  methods: ['GET'],
  route: 'payments/stats',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Fetching payment statistics')
    return await paymentHandler.getPaymentStatistics(req)
  }
})

/**
 * Search Payments
 * GET /api/payments/search?organizationId=xxx&status=completed&paymentType=consultation&startDate=2024-01-01&endDate=2024-12-31
 * 
 * Test Flow:
 * 1. Advanced filtering for payment records
 * 2. Filter by status, type, date range, amount range
 * 3. Useful for reporting and reconciliation
 */
app.http('search-payments', {
  methods: ['GET'],
  route: 'payments/search',
  authLevel: 'function',
  handler: async (req, context) => {
    context.log('Searching payments')
    return await paymentHandler.searchPayments(req)
  }
})

app.http('payment', {
  methods: ['GET', 'POST', 'PATCH'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Payment function processed request for url "${req.url}"`)
    
    const decode = context.extraInputs.get('decode')
    
    switch (req.method) {
      case HttpMethod.get:
        const paymentId = req.query.get('paymentId')
        const organizationId = req.query.get('organizationId')
        
        if (paymentId) {
          return await paymentHandler.getPaymentDetails(req)
        } else if (organizationId) {
          return await paymentHandler.getOrganizationPayments(req)
        } else {
          return jsonResponse(
            'Missing required parameters (paymentId or organizationId)',
            HttpStatusCode.BadRequest
          )
        }
        
      case HttpMethod.post:
        const body = await req.json()
        
        if (body.razorpay_order_id && body.razorpay_payment_id && body.razorpay_signature) {
          return await paymentHandler.verifyPayment(req)
        } else {
          return await paymentHandler.createOrder(req)
        }
        
      case HttpMethod.patch:
        return jsonResponse(
          'PATCH operations not implemented yet',
          HttpStatusCode.NotImplemented
        )
        
      default:
        return jsonResponse(
          'Unsupported HTTP method',
          HttpStatusCode.MethodNotAllowed
        )
    }
  }
})
