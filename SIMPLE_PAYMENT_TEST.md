# Simple Payment Integration Test Guide

## Overview

This guide provides simple curl commands to test the Razorpay payment integration. You only need the core payment APIs - no complex integration with other handlers.

## Core Payment Flow

1. **Create Order** → Get payment order from Razorpay
2. **Frontend Checkout** → User pays via Razorpay (simulated)
3. **Verify Payment** → Verify payment signature
4. **Complete Action** → Proceed with original business logic

## Test Commands

### 1. Patient Registration Payment

#### Step 1: Create Payment Order
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 200,
    "currency": "INR",
    "paymentType": "patient_registration",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Registration Fee for John Doe",
    "metadata": {
      "patientName": "John Doe",
      "organizationName": "Test Hospital"
    }
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "orderId": "order_xyz123",
    "paymentId": "internal-payment-id",
    "keyId": "rzp_test_CUHJLaG7X7H8uG",
    "amount": 20000,
    "currency": "INR",
    "status": "created"
  }
}
```

#### Step 2: Verify Payment (Simulate)
```bash
curl -X POST "http://localhost:7071/api/payments/verify" \
  -H "Content-Type: application/json" \
  -d '{
    "razorpay_order_id": "order_xyz123",
    "razorpay_payment_id": "pay_test123",
    "razorpay_signature": "test_signature_hash"
  }'
```

### 2. Consultation Payment

#### Create Payment Order
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 500,
    "currency": "INR",
    "paymentType": "consultation",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Consultation Fee for Dr. Smith",
    "metadata": {
      "appointmentId": "test-apt-001",
      "doctorId": "test-doctor-001",
      "doctorName": "Dr. Smith",
      "patientName": "John Doe",
      "appointmentDate": "2025-01-15",
      "appointmentTime": "10:00 AM"
    }
  }'
```

### 3. Prescription Payment

#### Create Payment Order
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 220,
    "currency": "INR",
    "paymentType": "prescription",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Prescription Medicines",
    "metadata": {
      "prescriptionId": "test-presc-001",
      "patientName": "John Doe",
      "medicineCount": 2,
      "totalCost": 220
    }
  }'
```

### 4. Lab Test Payment

#### Create Payment Order
```bash
curl -X POST "http://localhost:7071/api/payments/create-order" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 450,
    "currency": "INR",
    "paymentType": "lab_test",
    "patientId": "test-patient-001",
    "organizationId": "test-org-001",
    "description": "Lab Tests Payment",
    "metadata": {
      "labTestOrderId": "test-lab-order-001",
      "testIds": ["test-01", "test-02"],
      "testNames": ["Blood Test", "Urine Test"],
      "testCount": 2,
      "totalCost": 450
    }
  }'
```

## Management APIs

### Get Payment Details
```bash
curl -X GET "http://localhost:7071/api/payments/details?paymentId=internal-payment-id"
```

### Get Organization Payments (Fixed Pagination)
```bash
# First page
curl -X GET "http://localhost:7072/api/payments/organization?organizationId=43cac26c-c3fb-45e7-bfa2-dd59db9f13fd&pageSize=2" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Ocp-Apim-Subscription-Key: ac5d8ae93c0f4f9892390f9c9646e749"

# Next page (use continuationToken from previous response)
curl -X GET "http://localhost:7072/api/payments/organization?organizationId=43cac26c-c3fb-45e7-bfa2-dd59db9f13fd&pageSize=2&continuationToken=CONTINUATION_TOKEN_HERE" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Ocp-Apim-Subscription-Key: ac5d8ae93c0f4f9892390f9c9646e749"
```

**Expected Response (Fixed):**
```json
{
  "payments": [
    {
      "id": "payment-id-1",
      "amount": 20000,
      "status": "completed",
      "paymentType": "prescription",
      "patientId": "CG94304351",
      "organizationId": "43cac26c-c3fb-45e7-bfa2-dd59db9f13fd",
      "description": "Test Consultation Fee",
      "createdAt": "2025-08-05T07:32:53.478Z",
      "verifiedAt": "2025-08-05T07:40:51.112Z",
      "metadata": {
        "prescriptionId": "36f693a4-da6a-484a-8e55-05ece3d84a68",
        "patientName": "MUBEENA",
        "medicineCount": 4
      }
    }
  ],
  "continuationToken": "next-page-token-here",
  "hasMoreResults": true,
  "pageSize": 2,
  "totalCount": 15,
  "totalPages": 8,
  "currentPage": 1,
  "totalFetched": 2
}
```

### Get Payment Statistics
```bash
curl -X GET "http://localhost:7071/api/payments/stats?organizationId=test-org-001"
```

### Search Payments
```bash
curl -X GET "http://localhost:7071/api/payments/search?organizationId=test-org-001&status=completed&paymentType=consultation"
```

## Frontend Integration (Simple)

```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body>
    <button onclick="payForRegistration()">Pay Registration Fee</button>
    <button onclick="payForConsultation()">Pay Consultation Fee</button>
    
    <script>
    async function payForRegistration() {
        // Step 1: Create order
        const orderResponse = await fetch('/api/payments/create-order', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                amount: 200,
                paymentType: 'patient_registration',
                patientId: 'patient-123',
                organizationId: 'org-456',
                description: 'Registration Fee',
                metadata: {
                    patientName: 'John Doe',
                    organizationName: 'City Hospital'
                }
            })
        });
        
        const orderData = await orderResponse.json();
        
        // Step 2: Open Razorpay checkout
        const options = {
            key: orderData.data.keyId,
            amount: orderData.data.amount,
            currency: orderData.data.currency,
            order_id: orderData.data.orderId,
            name: 'City Hospital',
            description: orderData.data.description,
            handler: async function(response) {
                // Step 3: Verify payment
                const verifyResponse = await fetch('/api/payments/verify', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        razorpay_order_id: response.razorpay_order_id,
                        razorpay_payment_id: response.razorpay_payment_id,
                        razorpay_signature: response.razorpay_signature
                    })
                });
                
                const verifyData = await verifyResponse.json();
                if (verifyData.verified) {
                    alert('Payment successful! You can now complete registration.');
                    // Redirect to registration completion or success page
                    window.location.href = '/registration-success';
                } else {
                    alert('Payment verification failed!');
                }
            },
            prefill: {
                name: 'John Doe',
                email: '<EMAIL>',
                contact: '**********'
            }
        };
        
        const rzp = new Razorpay(options);
        rzp.open();
    }
    
    async function payForConsultation() {
        // Similar implementation for consultation payment
        // Just change the paymentType and metadata
    }
    </script>
</body>
</html>
```

## Integration with Existing APIs

### After Payment Success, Call Existing APIs:

1. **Patient Registration**: After payment verification, call existing patient creation API
2. **Appointment Booking**: After payment verification, call existing appointment creation API  
3. **Prescription**: After payment verification, call existing prescription creation API
4. **Lab Test**: After payment verification, call existing lab test creation API

### Example Integration Flow:

```javascript
// After successful payment verification
if (paymentVerified) {
    switch (paymentType) {
        case 'patient_registration':
            // Call existing patient creation API
            await fetch('/api/patient', {
                method: 'POST',
                body: JSON.stringify(patientData)
            });
            break;
            
        case 'consultation':
            // Call existing appointment creation API
            await fetch('/api/appointment', {
                method: 'POST', 
                body: JSON.stringify(appointmentData)
            });
            break;
            
        case 'prescription':
            // Call existing prescription creation API
            await fetch('/api/prescriptions', {
                method: 'POST',
                body: JSON.stringify(prescriptionData)
            });
            break;
            
        case 'lab_test':
            // Call existing lab test creation API
            await fetch('/api/patient-lab-test', {
                method: 'POST',
                body: JSON.stringify(labTestData)
            });
            break;
    }
}
```

## Key Benefits of This Approach

1. **No Changes to Existing APIs**: Your current patient, appointment, prescription, and lab test APIs remain unchanged
2. **Modular Payment**: Payment is handled separately and can be integrated anywhere
3. **Simple Frontend**: Just add payment flow before calling existing APIs
4. **Easy Testing**: Each payment type can be tested independently
5. **Flexible Integration**: Can be added to any existing workflow

## Testing Checklist

- [ ] Create payment order for each type
- [ ] Verify payment signature validation
- [ ] Test webhook processing
- [ ] Check payment details retrieval
- [ ] Verify organization payment listing
- [ ] Test payment statistics
- [ ] Validate error handling for invalid requests
