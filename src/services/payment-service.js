const Razorpay = require('razorpay')
const crypto = require('crypto')
const { v4: uuidv4 } = require('uuid')
const paymentRepository = require('../repositories/payment-repository')
const PaymentModel = require('../models/payment-model')
const { PaymentStatus, PaymentType } = require('../common/constant')
const logging = require('../common/logging')

class PaymentService {
  constructor() {
    this.razorpay = null
    this.webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET
  }

  getRazorpayInstance() {
    if (!this.razorpay) {
      if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_KEY_SECRET) {
        throw new Error('Razorpay credentials not configured')
      }
      this.razorpay = new Razorpay({
        key_id: process.env.RAZORPAY_KEY_ID,
        key_secret: process.env.RAZORPAY_KEY_SECRET,
      })
    }
    return this.razorpay
  }

  async createOrder(paymentData) {
    try {
      const payment = new PaymentModel(paymentData)

      const orderOptions = {
        amount: payment.amount,
        currency: payment.currency,
        receipt: `receipt_${Date.now()}`,
        notes: {
          paymentType: payment.paymentType,
          patientId: payment.patientId,
          organizationId: payment.organizationId,
          description: payment.description,
          ...payment.metadata,
        },
      }

      const razorpayOrder = await this.getRazorpayInstance().orders.create(
        orderOptions,
      )

      payment.razorpayOrderId = razorpayOrder.id
      payment.id = uuidv4()

      const savedPayment = await paymentRepository.createPayment(payment)

      return {
        success: true,
        data: {
          orderId: razorpayOrder.id,
          paymentId: savedPayment.id,
          keyId: process.env.RAZORPAY_KEY_ID,
          amount: payment.amount,
          currency: payment.currency,
          status: payment.status,
          description: payment.description,
        },
      }
    } catch (error) {
      logging.logError('Error creating payment order', error)
      throw new Error(`Failed to create payment order: ${error.message}`)
    }
  }

  async verifyPayment(verificationData) {
    try {
      const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
        verificationData

      const body = razorpay_order_id + '|' + razorpay_payment_id
      const expectedSignature = crypto
        .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
        .update(body.toString())
        .digest('hex')

      const isSignatureValid = expectedSignature === razorpay_signature

      if (isSignatureValid) {
        const payment = await paymentRepository.getPaymentByRazorpayOrderId(
          razorpay_order_id,
        )

        if (!payment) {
          throw new Error('Payment record not found')
        }

        payment.status = PaymentStatus.COMPLETED
        payment.razorpayPaymentId = razorpay_payment_id
        payment.razorpaySignature = razorpay_signature
        payment.verifiedAt = new Date().toISOString()

        await paymentRepository.updatePayment(payment)

        return {
          success: true,
          verified: true,
          message: 'Payment verified successfully',
          paymentId: razorpay_payment_id,
          payment: payment,
        }
      } else {
        return {
          success: false,
          verified: false,
          message: 'Invalid payment signature',
        }
      }
    } catch (error) {
      logging.logError('Error verifying payment', error)
      throw new Error(`Payment verification failed: ${error.message}`)
    }
  }

  async handleWebhook(webhookData, signature) {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(JSON.stringify(webhookData))
        .digest('hex')

      if (expectedSignature !== signature) {
        throw new Error('Invalid webhook signature')
      }

      const { event, payload } = webhookData

      const paymentEntity = payload.payment.entity

      const orderId = paymentEntity.order_id
      const paymentId = paymentEntity.id
      const paymentStatus = paymentEntity.status
      const errorDescription = paymentEntity.error_description || null

      const payment = await paymentRepository.getPaymentByRazorpayOrderId(
        orderId,
      )

      if (!payment) {
        logging.logError(`Payment record not found for order ID: ${orderId}`)
        return { success: false, message: 'Payment record not found' }
      }

      const paymentRecord = payment

      switch (event) {
        case 'payment.captured':
          paymentRecord.status = PaymentStatus.COMPLETED
          paymentRecord.razorpayPaymentId = paymentId
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        case 'payment.failed':
          paymentRecord.status = PaymentStatus.FAILED
          paymentRecord.failureReason = errorDescription || 'Payment failed'
          paymentRecord.razorpayPaymentId = paymentId
          paymentRecord.verifiedAt = new Date().toISOString()
          break

        default:
          logging.logInfo(`Unhandled webhook event: ${event}`)
          return { success: true, message: 'Event not processed' }
      }

      await paymentRepository.updatePayment(paymentRecord)

      return {
        success: true,
        message: `Webhook processed for event: ${event}`,
        paymentId: paymentRecord.id,
      }
    } catch (error) {
      logging.logError('Error handling webhook', error)
      throw new Error(`Webhook processing failed: ${error.message}`)
    }
  }

  async getPaymentById(id) {
    try {
      const payment = await paymentRepository.getPaymentById(id)

      if (!payment) {
        throw new Error('Payment not found')
      }

      return payment
    } catch (error) {
      logging.logError('Error fetching payment details', error)
      throw new Error(`Failed to fetch payment: ${error.message}`)
    }
  }

  async getPaymentsByOrganization(
    organizationId,
    pageSize = 20,
    continuationToken = null,
    includeTotalCount = false,
    filters = {},
  ) {
    try {
      const result = await paymentRepository.getPaymentsByOrganization(
        organizationId,
        pageSize,
        continuationToken,
        filters,
      )

      const response = {
        payments: result.items || [],
        continuationToken: result.continuationToken,
        hasMoreResults: result.hasMoreResults || false,
        pageSize: pageSize,
        itemCount: result.items ? result.items.length : 0,
      }

      if (includeTotalCount) {
        let countQuery = `SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = '${organizationId}'`

        if (filters.patientId) {
          countQuery += ` AND c.patientId = '${filters.patientId}'`
        }

        if (filters.status) {
          countQuery += ` AND c.status = '${filters.status}'`
        }

        if (filters.paymentType) {
          countQuery += ` AND c.paymentType = '${filters.paymentType}'`
        }

        if (filters.startDate) {
          countQuery += ` AND c.createdAt >= '${filters.startDate}'`
        }

        if (filters.endDate) {
          countQuery += ` AND c.createdAt <= '${filters.endDate}'`
        }

        if (filters.minAmount !== null && filters.minAmount !== undefined) {
          countQuery += ` AND c.amount >= ${filters.minAmount}`
        }

        if (filters.maxAmount !== null && filters.maxAmount !== undefined) {
          countQuery += ` AND c.amount <= ${filters.maxAmount}`
        }

        const countResult = await cosmosDbContext.queryItems(
          countQuery,
          paymentsContainer,
        )
        response.totalCount = countResult[0] || 0
      }

      return response
    } catch (error) {
      logging.logError('Error fetching organization payments', error)
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  async getPaymentsByOrganizationCursor(
    organizationId,
    pageSize = 20,
    cursor = null,
  ) {
    try {
      let query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}'`

      if (cursor) {
        query += ` AND c.createdAt < '${cursor}'`
      }

      query += ` ORDER BY c.createdAt DESC OFFSET 0 LIMIT ${pageSize + 1}`

      const result = await cosmosDbContext.queryItems(query, paymentsContainer)

      const hasMoreResults = result.length > pageSize
      const payments = hasMoreResults ? result.slice(0, pageSize) : result
      const nextCursor = hasMoreResults
        ? payments[payments.length - 1].createdAt
        : null

      return {
        payments,
        nextCursor,
        hasMoreResults,
        pageSize,
        itemCount: payments.length,
      }
    } catch (error) {
      logging.logError(
        'Error fetching organization payments with cursor',
        error,
      )
      throw new Error(`Failed to fetch payments: ${error.message}`)
    }
  }

  async getPaymentStatistics(organizationId) {
    try {
      const query = `SELECT * FROM c WHERE c.organizationId = '${organizationId}' AND c.status = '${PaymentStatus.COMPLETED}'`
      const payments = await cosmosDbContext.queryItems(
        query,
        paymentsContainer,
      )

      const totalAmount = payments.reduce(
        (sum, payment) => sum + payment.amount,
        0,
      )
      const totalCount = payments.length

      const paymentTypeStats = {}
      payments.forEach((payment) => {
        if (!paymentTypeStats[payment.paymentType]) {
          paymentTypeStats[payment.paymentType] = {
            count: 0,
            amount: 0,
          }
        }
        paymentTypeStats[payment.paymentType].count++
        paymentTypeStats[payment.paymentType].amount += payment.amount
      })

      const monthlyStats = {}
      payments.forEach((payment) => {
        const month = new Date(payment.createdAt).toISOString().substring(0, 7)
        if (!monthlyStats[month]) {
          monthlyStats[month] = {
            count: 0,
            amount: 0,
          }
        }
        monthlyStats[month].count++
        monthlyStats[month].amount += payment.amount
      })

      return {
        totalAmount: totalAmount / 100,
        totalCount,
        paymentTypeBreakdown: Object.keys(paymentTypeStats).map((type) => ({
          type,
          count: paymentTypeStats[type].count,
          amount: paymentTypeStats[type].amount / 100,
        })),
        monthlyBreakdown: Object.keys(monthlyStats)
          .map((month) => ({
            month,
            count: monthlyStats[month].count,
            amount: monthlyStats[month].amount / 100,
          }))
          .sort((a, b) => a.month.localeCompare(b.month)),
      }
    } catch (error) {
      logging.logError('Error fetching payment statistics', error)
      throw new Error(`Failed to fetch payment statistics: ${error.message}`)
    }
  }
}

module.exports = new PaymentService()
