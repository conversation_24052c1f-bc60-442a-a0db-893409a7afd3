const openAIService = require('../services/openai-service')
const lifeStyleService = require('../services/lifestyle-service')
const logging = require('../common/logging')
const helper = require('../common/helper')

class LifestyleAmbientHandler {
  async identifySpeaker(transcript) {
    try {
      var classification =
        'you are an AI assistant help doctor to separate the chat transcript to know which one is doctor which one is patient, following the format:\n\n[{"speaker": "doctor", "message": ""},{"speaker": "patient", "message": ""}]\n\ncombine into 1 inline JSON without \'\\n\'\n The result must be in the format above strictly, if you don\'t see any information to analytic then just return the json format above with the null value'
      var result = await openAIService.chatCompletion(
        classification,
        transcript,
      )
      return result
    } catch (error) {
      logging.logError('Unable to identify Speaker', error)
      return null
    }
  }

  async processLifestyleQuestions(conversation, source) {
    try {
      logging.logInfo(`Processing lifestyle questions for source: ${source}`)

      // Fetch questions from database using the existing service
      const questionsData = await lifeStyleService.getLifeStyeBySourceName(
        source,
      )
      console.log(questionsData, 'kkkkkkkkkkkkk')

      if (!questionsData || !Array.isArray(questionsData)) {
        logging.logError(`No questions found for source: ${source}`)
        return {}
      }

      const summary = {}
      for (const doc of questionsData) {
        if (doc.questions && Array.isArray(doc.questions)) {
          doc.questions.forEach((question, index) => {
            console.log(`Question ${index + 1}: ${question.title}`)

            if (question.fields && Array.isArray(question.fields)) {
              question.fields.forEach((field, fieldIndex) => {
                console.log(`  Field ${fieldIndex + 1}:`)
                console.log(`    ID: ${field.id}`)
                console.log(`    Label: ${field.label}`)
                console.log(`    Type: ${field.type}`)
                if (field.options) {
                  console.log(`    Options: ${field.options.join(', ')}`)
                }
              })
            }
          })
        }
      }

      // // Process each question document from the database
      // for (const questionDoc of questionsData) {
      //   if (questionDoc.questions && Array.isArray(questionDoc.questions)) {
      //     for (const questionGroup of questionDoc.questions) {
      //       if (
      //         questionGroup.questions &&
      //         Array.isArray(questionGroup.questions)
      //       ) {
      //         console.log(questionGroup.questions, 'questionGroup.questions')
      //         for (const question of questionGroup.questions) {
      //           if (question.fields && Array.isArray(question.fields)) {
      //             for (const field of question.fields) {
      //               if (field.label) {
      //                 // Create a comprehensive prompt for extracting lifestyle information
      //                 const prompt = this.createLifestylePrompt(
      //                   field.label,
      //                   conversation,
      //                 )
      //                 console.log(prompt, 'prompt')

      //                 try {
      //                   const answer = await openAIService.chatCompletion(
      //                     prompt,
      //                     conversation,
      //                   )

      //                   // Use field label as key, clean it for object property
      //                   const fieldKey = this.sanitizeFieldKey(field.label)

      //                   // Clean up the answer - remove quotes and trim
      //                   let cleanAnswer = answer ? answer.toString().trim() : ''
      //                   if (
      //                     cleanAnswer.startsWith('"') &&
      //                     cleanAnswer.endsWith('"')
      //                   ) {
      //                     cleanAnswer = cleanAnswer.slice(1, -1)
      //                   }

      //                   summary[fieldKey] = cleanAnswer
      //                 } catch (error) {
      //                   logging.logError(
      //                     `Error processing field ${field.label}:`,
      //                     error,
      //                   )
      //                   const fieldKey = this.sanitizeFieldKey(field.label)
      //                   summary[fieldKey] = ''
      //                 }
      //               }
      //             }
      //           }
      //         }
      //       }
      //     }
      //   }
      // }
      // Process each question document from the database
      for (const questionDoc of questionsData) {
        if (questionDoc.questions && Array.isArray(questionDoc.questions)) {
          console.log('ggggggggggggggggggg')

          for (const question of questionDoc.questions) {
            if (question.fields && Array.isArray(question.fields)) {
              for (const field of question.fields) {
                if (field.label) {
                  // Create a comprehensive prompt for extracting lifestyle information
                  const prompt = this.createLifestylePrompt(
                    field.label,
                    conversation,
                  )
                  console.log(prompt, 'prompt')

                  try {
                    const answer = await openAIService.chatCompletion(
                      prompt,
                      conversation,
                    )

                    // Use field label as key, clean it for object property
                    const fieldKey = this.sanitizeFieldKey(field.label)

                    // Clean up the answer - remove quotes and trim
                    let cleanAnswer = answer ? answer.toString().trim() : ''
                    if (
                      cleanAnswer.startsWith('"') &&
                      cleanAnswer.endsWith('"')
                    ) {
                      cleanAnswer = cleanAnswer.slice(1, -1)
                    }

                    summary[fieldKey] = cleanAnswer
                  } catch (error) {
                    logging.logError(
                      `Error processing field ${field.label}:`,
                      error,
                    )
                    const fieldKey = this.sanitizeFieldKey(field.label)
                    summary[fieldKey] = ''
                  }
                }
              }
            }
          }
        }
      }

      return summary
    } catch (error) {
      logging.logError('Unable to process lifestyle questions', error)
      return {}
    }
  }

  createLifestylePrompt(fieldLabel, conversation) {
    return `You are a medical AI assistant analyzing a conversation between a doctor and patient for lifestyle assessment.

From the following conversation, extract information specifically related to: "${fieldLabel}"

Instructions:
1. Focus only on information directly related to "${fieldLabel}"
2. If the information is clearly mentioned, provide a concise, factual answer
3. If the information is not mentioned, unclear, or ambiguous, respond with ""
4. Do not make assumptions or infer information not explicitly stated
5. Keep responses medical-appropriate and factual
6. Format as plain text without HTML tags or formatting
7. For lifestyle questions, focus on patient's habits, behaviors, frequency, duration, and specific details
8. Extract exact quotes or paraphrased information from the patient's responses

Question: ${fieldLabel}
Answer:`
  }

  sanitizeFieldKey(label) {
    // Convert field label to a valid object key
    return label
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '') // Remove spaces
      .trim()
  }

  async processLifestyleAmbientListening(transcript, source) {
    try {
      logging.logInfo(
        `Processing lifestyle ambient listening for source: ${source}`,
      )

      // Step 1: Identify speakers in the conversation
      const conversation = await this.identifySpeaker(transcript)
      const objConversation = helper.parseJSON(conversation)

      if (!objConversation || !Array.isArray(objConversation)) {
        logging.logError('Failed to parse conversation')
        return {
          conversation: [],
          summary: {},
        }
      }

      // Step 2: Process lifestyle questions and extract answers
      const summary = await this.processLifestyleQuestions(conversation, source)

      return {
        conversation: objConversation,
        summary: summary,
      }
    } catch (error) {
      logging.logError('Unable to process lifestyle ambient listening', error)
      return {
        conversation: [],
        summary: {},
      }
    }
  }
}

module.exports = new LifestyleAmbientHandler()
